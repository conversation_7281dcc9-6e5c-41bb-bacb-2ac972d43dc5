function [newPath1]=drawPC_duibi(result,result2,result3,result4,result5,option,data,str)

path0=[];
for noP=1:length(data.noE0)
    path=result.path{noP};
    if noP==1
        index1=1;
        index2=2;
    else
        index1=2;
        index2=3;
    end
    while 1

        if index2>length(path(:,1))
            break;
        end
        nowP=path(index1,2:4);
        aimP=path(index2,2:4);
        flag=0;
        for i=1:1000
            nowP0=nowP+(aimP-nowP)*i/1000;
            x0=round(nowP0(1));
            y0=round(nowP0(2));
            H0=data.map_z(x0,y0);
            x1=ceil(nowP0(1));
            y1=round(nowP0(2));
            H1=data.map_z(x1,y1);
            x2=round(nowP0(1));
            y2=ceil(nowP0(2));
            H2=data.map_z(x2,y2);
            x3=ceil(nowP0(1));
            y3=ceil(nowP0(2));
            H3=data.map_z(x3,y3);
            if min([H0,H1,H2,H3])>nowP0(3)
                flag=1;
            end
        end
        if flag==1
            path0=[path0;nowP;path(index2-1,2:4)];
            index1=index2;
            index2=index1+1;

        else
            index2=index2+1;
        end
    end
    path0=[path0;nowP;aimP];
end
% path=path0;
% path0=path(:,2:4);
figure(188)
plot3(data.S(:,2),data.S(:,1),data.S(:,3),'o','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
hold on
plot3(data.E0(:,2),data.E0(:,1),data.E0(:,3),'h','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
mesh(data.map_x,data.map_y,data.map_z); %生成由X，Y和Z指定的网线面
newPath1=[];
tempN=[];
while 1
    if length(path0(:,1))>=2
        if isempty(tempN)
            tempN=[path0(1:2,:)];
            nextInd=3;
        else
            tempN=[tempN(end,:);path0(1,:)];
            nextInd=2;
        end
        flag=ones(1,2);
        type=zeros(1,2);
        for j=1:2
            if tempN(1,j)==tempN(2,j)
                flag(j)=0;
            end
            if tempN(1,j)<tempN(2,j)
                type(j)=1;

            else
                type(j)=2;
            end
        end
    else
        for i=1:length(path0(:,1))
            newPath1=[newPath1;path0(i,1:3);];
        end
    end

    biaoji=1;
    for i=nextInd:length(path0(:,1))
        flag0=flag;
        for j=1:2
            if type(j)==1
                if tempN(end,j)>=path0(i,j)
                    flag0(j)=0;
                end
            else
                if tempN(end,j)<=path0(i,j)
                    flag0(j)=0;
                end
            end
        end
        if sum(flag0)==0
            if length(tempN(:,1))>2
                tempN(end,:)=tempN(end-1,:)+0.5*(tempN(end,:)-tempN(end-1,:));
            end
            p=find(flag==1);
            p=p(1);
            X=tempN(:,1);
            Y=tempN(:,2);
            Z=tempN(:,3);
            if p==1
                xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
                yi=interp1(X,Y, xi', 'pchip');
                zi=interp1(X,Z, xi', 'pchip');
                newPath1=[newPath1;xi',yi,zi];
            else
                yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
                xi=interp1(Y,X,yi', 'pchip');
                zi=interp1(Y,Z,yi', 'pchip');
                newPath1=[newPath1;xi,yi',zi];
            end
            biaoji=0;
            path0(1:i-1,:)=[];
            break;
        else
            flag=flag0;
            tempN=[tempN;path0(i,:)];
        end
    end
    if biaoji==1
        p=find(flag==1);
        p=p(1);
        X=tempN(:,1);
        Y=tempN(:,2);
        Z=tempN(:,3);
        if p==1
            xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
            yi=interp1(X,Y, xi', 'pchip');
            zi=interp1(X,Z, xi', 'pchip');
            newPath1=[newPath1;xi',yi,zi];
        else
            yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
            xi=interp1(Y,X,yi', 'pchip');
            zi=interp1(Y,Z,yi', 'pchip');
            newPath1=[newPath1;xi,yi',zi];
        end
        break;
    end
    %newPath=[newPath;path0(1,:)];
%     plot3(newPath(:,2),newPath(:,1),newPath(:,3),'-','LineWidth',2,...
%     'MarkerEdgeColor','k',...
%     'MarkerFaceColor','r',...
%     'MarkerSize',10)
end
%plot(newPath(:,1),newPath(:,2), 'LineWidth', 2,'Color','m')
%newPath=path0;
for i=1:length(newPath1(:,2))
    nowP0=newPath1(i,:);
    x0=round(nowP0(1));
    y0=round(nowP0(2));
    H0=data.map_z(x0,y0);
%     x1=ceil(nowP0(1));
%     y1=round(nowP0(2));
%     H1=data.map_z(x1,y1);
%     x2=round(nowP0(1));
%     y2=ceil(nowP0(2));
%     H2=data.map_z(x2,y2);
%     x3=ceil(nowP0(1));
%     y3=ceil(nowP0(2));
%     H3=data.map_z(x3,y3);
    %newPath(i,3)=max([H0+1,H1+1,H2+1,H3+1,nowP0(3)]);
    newPath1(i,3)=max([H0+1,nowP0(3)]);
end

path0=[];
for noP=1:length(data.noE0)
    path=result2.path{noP};
    if noP==1
        index1=1;
        index2=2;
    else
        index1=2;
        index2=3;
    end
    while 1

        if index2>length(path(:,1))
            break;
        end
        nowP=path(index1,2:4);
        aimP=path(index2,2:4);
        flag=0;
        for i=1:1000
            nowP0=nowP+(aimP-nowP)*i/1000;
            x0=round(nowP0(1));
            y0=round(nowP0(2));
            H0=data.map_z(x0,y0);
            x1=ceil(nowP0(1));
            y1=round(nowP0(2));
            H1=data.map_z(x1,y1);
            x2=round(nowP0(1));
            y2=ceil(nowP0(2));
            H2=data.map_z(x2,y2);
            x3=ceil(nowP0(1));
            y3=ceil(nowP0(2));
            H3=data.map_z(x3,y3);
            if min([H0,H1,H2,H3])>nowP0(3)
                flag=1;
            end
        end
        if flag==1
            path0=[path0;nowP;path(index2-1,2:4)];
            index1=index2;
            index2=index1+1;

        else
            index2=index2+1;
        end
    end
    path0=[path0;nowP;aimP];
end
% path=path0;
% path0=path(:,2:4);
figure(266)
plot3(data.S(:,2),data.S(:,1),data.S(:,3),'o','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
hold on
plot3(data.E0(:,2),data.E0(:,1),data.E0(:,3),'h','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
mesh(data.map_x,data.map_y,data.map_z); %生成由X，Y和Z指定的网线面
newPath2=[];
tempN=[];
while 1
    if length(path0(:,1))>=2
        if isempty(tempN)
            tempN=[path0(1:2,:)];
            nextInd=3;
        else
            tempN=[tempN(end,:);path0(1,:)];
            nextInd=2;
        end
        flag=ones(1,2);
        type=zeros(1,2);
        for j=1:2
            if tempN(1,j)==tempN(2,j)
                flag(j)=0;
            end
            if tempN(1,j)<tempN(2,j)
                type(j)=1;

            else
                type(j)=2;
            end
        end
    else
        for i=1:length(path0(:,1))
            newPath2=[newPath2;path0(i,1:3);];
        end
    end

    biaoji=1;
    for i=nextInd:length(path0(:,1))
        flag0=flag;
        for j=1:2
            if type(j)==1
                if tempN(end,j)>=path0(i,j)
                    flag0(j)=0;
                end
            else
                if tempN(end,j)<=path0(i,j)
                    flag0(j)=0;
                end
            end
        end
        if sum(flag0)==0
            if length(tempN(:,1))>2
                tempN(end,:)=tempN(end-1,:)+0.5*(tempN(end,:)-tempN(end-1,:));
            end
            p=find(flag==1);
            p=p(1);
            X=tempN(:,1);
            Y=tempN(:,2);
            Z=tempN(:,3);
            if p==1
                xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
                yi=interp1(X,Y, xi', 'pchip');
                zi=interp1(X,Z, xi', 'pchip');
                newPath2=[newPath2;xi',yi,zi];
            else
                yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
                xi=interp1(Y,X,yi', 'pchip');
                zi=interp1(Y,Z,yi', 'pchip');
                newPath2=[newPath2;xi,yi',zi];
            end
            biaoji=0;
            path0(1:i-1,:)=[];
            break;
        else
            flag=flag0;
            tempN=[tempN;path0(i,:)];
        end
    end
    if biaoji==1
        p=find(flag==1);
        p=p(1);
        X=tempN(:,1);
        Y=tempN(:,2);
        Z=tempN(:,3);
        if p==1
            xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
            yi=interp1(X,Y, xi', 'pchip');
            zi=interp1(X,Z, xi', 'pchip');
            newPath2=[newPath2;xi',yi,zi];
        else
            yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
            xi=interp1(Y,X,yi', 'pchip');
            zi=interp1(Y,Z,yi', 'pchip');
            newPath2=[newPath2;xi,yi',zi];
        end
        break;
    end
    %newPath=[newPath;path0(1,:)];
%     plot3(newPath(:,2),newPath(:,1),newPath(:,3),'-','LineWidth',2,...
%     'MarkerEdgeColor','k',...
%     'MarkerFaceColor','r',...
%     'MarkerSize',10)
end
%plot(newPath(:,1),newPath(:,2), 'LineWidth', 2,'Color','m')
%newPath=path0;
for i=1:length(newPath2(:,2))
    nowP0=newPath2(i,:);
    x0=round(nowP0(1));
    y0=round(nowP0(2));
    H0=data.map_z(x0,y0);
%     x1=ceil(nowP0(1));
%     y1=round(nowP0(2));
%     H1=data.map_z(x1,y1);
%     x2=round(nowP0(1));
%     y2=ceil(nowP0(2));
%     H2=data.map_z(x2,y2);
%     x3=ceil(nowP0(1));
%     y3=ceil(nowP0(2));
%     H3=data.map_z(x3,y3);
    %newPath(i,3)=max([H0+1,H1+1,H2+1,H3+1,nowP0(3)]);
    newPath2(i,3)=max([H0+1,nowP0(3)]);
end
path0=[];
for noP=1:length(data.noE0)
    path=result3.path{noP};
    if noP==1
        index1=1;
        index2=2;
    else
        index1=2;
        index2=3;
    end
    while 1

        if index2>length(path(:,1))
            break;
        end
        nowP=path(index1,2:4);
        aimP=path(index2,2:4);
        flag=0;
        for i=1:1000
            nowP0=nowP+(aimP-nowP)*i/1000;
            x0=round(nowP0(1));
            y0=round(nowP0(2));
            H0=data.map_z(x0,y0);
            x1=ceil(nowP0(1));
            y1=round(nowP0(2));
            H1=data.map_z(x1,y1);
            x2=round(nowP0(1));
            y2=ceil(nowP0(2));
            H2=data.map_z(x2,y2);
            x3=ceil(nowP0(1));
            y3=ceil(nowP0(2));
            H3=data.map_z(x3,y3);
            if min([H0,H1,H2,H3])>nowP0(3)
                flag=1;
            end
        end
        if flag==1
            path0=[path0;nowP;path(index2-1,2:4)];
            index1=index2;
            index2=index1+1;

        else
            index2=index2+1;
        end
    end
    path0=[path0;nowP;aimP];
end
% path=path0;
% path0=path(:,2:4);
figure(266)
plot3(data.S(:,2),data.S(:,1),data.S(:,3),'o','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
hold on
plot3(data.E0(:,2),data.E0(:,1),data.E0(:,3),'h','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
mesh(data.map_x,data.map_y,data.map_z); %生成由X，Y和Z指定的网线面
newPath3=[];
tempN=[];
while 1
    if length(path0(:,1))>=2
        if isempty(tempN)
            tempN=[path0(1:2,:)];
            nextInd=3;
        else
            tempN=[tempN(end,:);path0(1,:)];
            nextInd=2;
        end
        flag=ones(1,2);
        type=zeros(1,2);
        for j=1:2
            if tempN(1,j)==tempN(2,j)
                flag(j)=0;
            end
            if tempN(1,j)<tempN(2,j)
                type(j)=1;

            else
                type(j)=2;
            end
        end
    else
        for i=1:length(path0(:,1))
            newPath3=[newPath3;path0(i,1:3);];
        end
    end

    biaoji=1;
    for i=nextInd:length(path0(:,1))
        flag0=flag;
        for j=1:2
            if type(j)==1
                if tempN(end,j)>=path0(i,j)
                    flag0(j)=0;
                end
            else
                if tempN(end,j)<=path0(i,j)
                    flag0(j)=0;
                end
            end
        end
        if sum(flag0)==0
            if length(tempN(:,1))>2
                tempN(end,:)=tempN(end-1,:)+0.5*(tempN(end,:)-tempN(end-1,:));
            end
            p=find(flag==1);
            p=p(1);
            X=tempN(:,1);
            Y=tempN(:,2);
            Z=tempN(:,3);
            if p==1
                xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
                yi=interp1(X,Y, xi', 'pchip');
                zi=interp1(X,Z, xi', 'pchip');
                newPath3=[newPath3;xi',yi,zi];
            else
                yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
                xi=interp1(Y,X,yi', 'pchip');
                zi=interp1(Y,Z,yi', 'pchip');
                newPath3=[newPath3;xi,yi',zi];
            end
            biaoji=0;
            path0(1:i-1,:)=[];
            break;
        else
            flag=flag0;
            tempN=[tempN;path0(i,:)];
        end
    end
    if biaoji==1
        p=find(flag==1);
        p=p(1);
        X=tempN(:,1);
        Y=tempN(:,2);
        Z=tempN(:,3);
        if p==1
            xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
            yi=interp1(X,Y, xi', 'pchip');
            zi=interp1(X,Z, xi', 'pchip');
            newPath3=[newPath3;xi',yi,zi];
        else
            yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
            xi=interp1(Y,X,yi', 'pchip');
            zi=interp1(Y,Z,yi', 'pchip');
            newPath3=[newPath3;xi,yi',zi];
        end
        break;
    end
    %newPath=[newPath;path0(1,:)];
%     plot3(newPath(:,2),newPath(:,1),newPath(:,3),'-','LineWidth',2,...
%     'MarkerEdgeColor','k',...
%     'MarkerFaceColor','r',...
%     'MarkerSize',10)
end
%plot(newPath(:,1),newPath(:,2), 'LineWidth', 2,'Color','m')
%newPath=path0;
for i=1:length(newPath3(:,2))
    nowP0=newPath3(i,:);
    x0=round(nowP0(1));
    y0=round(nowP0(2));
    H0=data.map_z(x0,y0);
%     x1=ceil(nowP0(1));
%     y1=round(nowP0(2));
%     H1=data.map_z(x1,y1);
%     x2=round(nowP0(1));
%     y2=ceil(nowP0(2));
%     H2=data.map_z(x2,y2);
%     x3=ceil(nowP0(1));
%     y3=ceil(nowP0(2));
%     H3=data.map_z(x3,y3);
    %newPath(i,3)=max([H0+1,H1+1,H2+1,H3+1,nowP0(3)]);
    newPath3(i,3)=max([H0+1,nowP0(3)]);
end
path0=[];
for noP=1:length(data.noE0)
    path=result4.path{noP};
    if noP==1
        index1=1;
        index2=2;
    else
        index1=2;
        index2=3;
    end
    while 1

        if index2>length(path(:,1))
            break;
        end
        nowP=path(index1,2:4);
        aimP=path(index2,2:4);
        flag=0;
        for i=1:1000
            nowP0=nowP+(aimP-nowP)*i/1000;
            x0=round(nowP0(1));
            y0=round(nowP0(2));
            H0=data.map_z(x0,y0);
            x1=ceil(nowP0(1));
            y1=round(nowP0(2));
            H1=data.map_z(x1,y1);
            x2=round(nowP0(1));
            y2=ceil(nowP0(2));
            H2=data.map_z(x2,y2);
            x3=ceil(nowP0(1));
            y3=ceil(nowP0(2));
            H3=data.map_z(x3,y3);
            if min([H0,H1,H2,H3])>nowP0(3)
                flag=1;
            end
        end
        if flag==1
            path0=[path0;nowP;path(index2-1,2:4)];
            index1=index2;
            index2=index1+1;

        else
            index2=index2+1;
        end
    end
    path0=[path0;nowP;aimP];
end
% path=path0;
% path0=path(:,2:4);
figure(266)
plot3(data.S(:,2),data.S(:,1),data.S(:,3),'o','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
hold on
plot3(data.E0(:,2),data.E0(:,1),data.E0(:,3),'h','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
mesh(data.map_x,data.map_y,data.map_z); %生成由X，Y和Z指定的网线面
newPath4=[];
tempN=[];
while 1
    if length(path0(:,1))>=2
        if isempty(tempN)
            tempN=[path0(1:2,:)];
            nextInd=3;
        else
            tempN=[tempN(end,:);path0(1,:)];
            nextInd=2;
        end
        flag=ones(1,2);
        type=zeros(1,2);
        for j=1:2
            if tempN(1,j)==tempN(2,j)
                flag(j)=0;
            end
            if tempN(1,j)<tempN(2,j)
                type(j)=1;

            else
                type(j)=2;
            end
        end
    else
        for i=1:length(path0(:,1))
            newPath4=[newPath4;path0(i,1:3);];
        end
    end

    biaoji=1;
    for i=nextInd:length(path0(:,1))
        flag0=flag;
        for j=1:2
            if type(j)==1
                if tempN(end,j)>=path0(i,j)
                    flag0(j)=0;
                end
            else
                if tempN(end,j)<=path0(i,j)
                    flag0(j)=0;
                end
            end
        end
        if sum(flag0)==0
            if length(tempN(:,1))>2
                tempN(end,:)=tempN(end-1,:)+0.5*(tempN(end,:)-tempN(end-1,:));
            end
            p=find(flag==1);
            p=p(1);
            X=tempN(:,1);
            Y=tempN(:,2);
            Z=tempN(:,3);
            if p==1
                xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
                yi=interp1(X,Y, xi', 'pchip');
                zi=interp1(X,Z, xi', 'pchip');
                newPath4=[newPath4;xi',yi,zi];
            else
                yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
                xi=interp1(Y,X,yi', 'pchip');
                zi=interp1(Y,Z,yi', 'pchip');
                newPath4=[newPath4;xi,yi',zi];
            end
            biaoji=0;
            path0(1:i-1,:)=[];
            break;
        else
            flag=flag0;
            tempN=[tempN;path0(i,:)];
        end
    end
    if biaoji==1
        p=find(flag==1);
        p=p(1);
        X=tempN(:,1);
        Y=tempN(:,2);
        Z=tempN(:,3);
        if p==1
            xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
            yi=interp1(X,Y, xi', 'pchip');
            zi=interp1(X,Z, xi', 'pchip');
            newPath4=[newPath4;xi',yi,zi];
        else
            yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
            xi=interp1(Y,X,yi', 'pchip');
            zi=interp1(Y,Z,yi', 'pchip');
            newPath4=[newPath4;xi,yi',zi];
        end
        break;
    end
    %newPath=[newPath;path0(1,:)];
%     plot3(newPath(:,2),newPath(:,1),newPath(:,3),'-','LineWidth',2,...
%     'MarkerEdgeColor','k',...
%     'MarkerFaceColor','r',...
%     'MarkerSize',10)
end
%plot(newPath(:,1),newPath(:,2), 'LineWidth', 2,'Color','m')
%newPath=path0;
for i=1:length(newPath4(:,2))
    nowP0=newPath4(i,:);
    x0=round(nowP0(1));
    y0=round(nowP0(2));
    H0=data.map_z(x0,y0);
%     x1=ceil(nowP0(1));
%     y1=round(nowP0(2));
%     H1=data.map_z(x1,y1);
%     x2=round(nowP0(1));
%     y2=ceil(nowP0(2));
%     H2=data.map_z(x2,y2);
%     x3=ceil(nowP0(1));
%     y3=ceil(nowP0(2));
%     H3=data.map_z(x3,y3);
    %newPath(i,3)=max([H0+1,H1+1,H2+1,H3+1,nowP0(3)]);
    newPath4(i,3)=max([H0+1,nowP0(3)]);
end
path0=[];
for noP=1:length(data.noE0)
    path=result5.path{noP};
    if noP==1
        index1=1;
        index2=2;
    else
        index1=2;
        index2=3;
    end
    while 1

        if index2>length(path(:,1))
            break;
        end
        nowP=path(index1,2:4);
        aimP=path(index2,2:4);
        flag=0;
        for i=1:1000
            nowP0=nowP+(aimP-nowP)*i/1000;
            x0=round(nowP0(1));
            y0=round(nowP0(2));
            H0=data.map_z(x0,y0);
            x1=ceil(nowP0(1));
            y1=round(nowP0(2));
            H1=data.map_z(x1,y1);
            x2=round(nowP0(1));
            y2=ceil(nowP0(2));
            H2=data.map_z(x2,y2);
            x3=ceil(nowP0(1));
            y3=ceil(nowP0(2));
            H3=data.map_z(x3,y3);
            if min([H0,H1,H2,H3])>nowP0(3)
                flag=1;
            end
        end
        if flag==1
            path0=[path0;nowP;path(index2-1,2:4)];
            index1=index2;
            index2=index1+1;

        else
            index2=index2+1;
        end
    end
    path0=[path0;nowP;aimP];
end
% path=path0;
% path0=path(:,2:4);
figure(266)
plot3(data.S(:,2),data.S(:,1),data.S(:,3),'o','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
hold on
plot3(data.E0(:,2),data.E0(:,1),data.E0(:,3),'h','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
mesh(data.map_x,data.map_y,data.map_z); %生成由X，Y和Z指定的网线面
newPath5=[];
tempN=[];
while 1
    if length(path0(:,1))>=2
        if isempty(tempN)
            tempN=[path0(1:2,:)];
            nextInd=3;
        else
            tempN=[tempN(end,:);path0(1,:)];
            nextInd=2;
        end
        flag=ones(1,2);
        type=zeros(1,2);
        for j=1:2
            if tempN(1,j)==tempN(2,j)
                flag(j)=0;
            end
            if tempN(1,j)<tempN(2,j)
                type(j)=1;

            else
                type(j)=2;
            end
        end
    else
        for i=1:length(path0(:,1))
            newPath5=[newPath5;path0(i,1:3);];
        end
    end

    biaoji=1;
    for i=nextInd:length(path0(:,1))
        flag0=flag;
        for j=1:2
            if type(j)==1
                if tempN(end,j)>=path0(i,j)
                    flag0(j)=0;
                end
            else
                if tempN(end,j)<=path0(i,j)
                    flag0(j)=0;
                end
            end
        end
        if sum(flag0)==0
            if length(tempN(:,1))>2
                tempN(end,:)=tempN(end-1,:)+0.5*(tempN(end,:)-tempN(end-1,:));
            end
            p=find(flag==1);
            p=p(1);
            X=tempN(:,1);
            Y=tempN(:,2);
            Z=tempN(:,3);
            if p==1
                xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
                yi=interp1(X,Y, xi', 'pchip');
                zi=interp1(X,Z, xi', 'pchip');
                newPath5=[newPath5;xi',yi,zi];
            else
                yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
                xi=interp1(Y,X,yi', 'pchip');
                zi=interp1(Y,Z,yi', 'pchip');
                newPath5=[newPath5;xi,yi',zi];
            end
            biaoji=0;
            path0(1:i-1,:)=[];
            break;
        else
            flag=flag0;
            tempN=[tempN;path0(i,:)];
        end
    end
    if biaoji==1
        p=find(flag==1);
        p=p(1);
        X=tempN(:,1);
        Y=tempN(:,2);
        Z=tempN(:,3);
        if p==1
            xi=tempN(1,1):(tempN(end,1)-tempN(1,1))/100:tempN(end,1);
            yi=interp1(X,Y, xi', 'pchip');
            zi=interp1(X,Z, xi', 'pchip');
            newPath5=[newPath5;xi',yi,zi];
        else
            yi=tempN(1,2):(tempN(end,2)-tempN(1,2))/100:tempN(end,2);
            xi=interp1(Y,X,yi', 'pchip');
            zi=interp1(Y,Z,yi', 'pchip');
            newPath5=[newPath5;xi,yi',zi];
        end
        break;
    end
    %newPath=[newPath;path0(1,:)];
%     plot3(newPath(:,2),newPath(:,1),newPath(:,3),'-','LineWidth',2,...
%     'MarkerEdgeColor','k',...
%     'MarkerFaceColor','r',...
%     'MarkerSize',10)
end
%plot(newPath(:,1),newPath(:,2), 'LineWidth', 2,'Color','m')
%newPath=path0;
for i=1:length(newPath5(:,2))
    nowP0=newPath5(i,:);
    x0=round(nowP0(1));
    y0=round(nowP0(2));
    H0=data.map_z(x0,y0);
%     x1=ceil(nowP0(1));
%     y1=round(nowP0(2));
%     H1=data.map_z(x1,y1);
%     x2=round(nowP0(1));
%     y2=ceil(nowP0(2));
%     H2=data.map_z(x2,y2);
%     x3=ceil(nowP0(1));
%     y3=ceil(nowP0(2));
%     H3=data.map_z(x3,y3);
    %newPath(i,3)=max([H0+1,H1+1,H2+1,H3+1,nowP0(3)]);
    newPath5(i,3)=max([H0+1,nowP0(3)]);
end
h1=plot3(newPath1(:,2),newPath1(:,1),newPath1(:,3),'-','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','r',...
    'MarkerSize',10)
hold on
h2=plot3(newPath2(:,2),newPath2(:,1),newPath2(:,3),'-','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','b',...
    'MarkerSize',10)
h3=plot3(newPath3(:,2),newPath3(:,1),newPath3(:,3),'-','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','g',...
    'MarkerSize',10)
h4=plot3(newPath3(:,2),newPath3(:,1),newPath3(:,3),'-','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','c',...
    'MarkerSize',10)
h5=plot3(newPath3(:,2),newPath3(:,1),newPath3(:,3),'-','LineWidth',2,...
    'MarkerEdgeColor','k',...
    'MarkerFaceColor','k',...
    'MarkerSize',10)
legend([h1, h2,h3,h4,h5], {'PGA路径', 'GGO 路径','ALA路径','FGO路径','RFO路径'}, 'Location', 'best');



data.map0=data.map;
data.mapSize0=size(data.map0);
% legend('起点','终点')

title('三维地形地图')
grid on
%axis equal
% title([str,'总目标:',num2str(result.fit)])
