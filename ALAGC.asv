% ALA算法优化函数，共有四种更新策略
function [best_position, hist_best] = ALA_optimization(fes_act_dec, fitness_func, predicted_steps)
    
    % 算法参数设置
    N = min(15, size(fes_act_dec, 1)); % 种群的大小
    Max_iter = 15; % 最大迭代次数
    vec_flag = [1, -1]; % 方向标志向量
    hist_best{1} = zeros(Max_iter, 2*predicted_steps);
    hist_best{2} = zeros(<PERSON>_iter, 1);
   
    % 初始化种群
    X = zeros(N, 2*predicted_steps);
    fitness = zeros(1, N);
    for i = 1:N
        decision_vector = [];
        for step = 1:predicted_steps
            idx = randi(size(fes_act_dec, 1));
            decision_vector = [decision_vector, fes_act_dec(idx, :)];
        end
        X(i, :) = decision_vector;
        fitness(1, i) = fitness_func(X(i, :));
    end
    
    % 找到初始最优解
    Position = zeros(1, 2*predicted_steps);
    Score = -inf;
    for i = 1:N
        if fitness(1, i) > Score
            Position = X(i, :);
            Score = fitness(1, i);
        end
    end
    
    Iter = 1;
    
    % 算法的主循环
    while Iter <= Max_iter
        RB = randn(N, 2*predicted_steps); % 布朗运动
        F = vec_flag(floor(2*rand()+1)); % 随机方向标志
        theta = 2*atan(1-Iter/Max_iter); % 时变参数
        p_cauchy = 1 - (Iter / Max_iter); % 柯西变异概率
        p_guassian = Iter / Max_iter; % 高斯变异概率
        % 位置更新阶段
        Xnew = zeros(N, 2*predicted_steps);
        for i = 1:N
            E = 2*log(1/rand)*theta;
            
            if E > 1 % 高能量状态
                % 柯西变异
                if rand < p_cauchy && rand > 0.3
                    cauchy_step = 1.5 - (Iter / Max_iter);
                    cauchy_mutate = cauchy_step.* tan(pi * (rand(1, 2*predicted_steps) - 0.5));
                    X(i, :) = Position + cauchy_mutate;
                if rand < 0.3
                    % 群体协作
                    r1 = 2 * rand(1, 2*predicted_steps) - 1;
                    Xnew(i,:) = Position + F.*RB(i,:).*(r1.*(Position-X(i,:))+(1-r1).*(X(i,:)-X(randi(N),:)));
                else
                    % 定向搜索
                    r2 = rand() * (1 + sin(0.5 * Iter));
                    Xnew(i,:) = X(i,:) + F.* r2*(Position-X(randi(N),:));
                end
                end
            else % 即E<=1，为低能量状态
                % 高斯变异
                if rand < p_guassian && rand > 0.5 
                    guassian_step = 1 - (Iter / Max_iter);
                    guassian_mutate = guassian_step.*randn(1,2*predicted_steps);
                    X(i, :) = Position + guassian_mutate;
                else
                    if rand < 0.5
                        % 螺旋搜索
                        radius = sqrt(sum((Position-X(i, :)).^2));
                        r3 = rand();
                        spiral = radius*(sin(2*pi*r3)+cos(2*pi*r3));
                        Xnew(i,:) = Position + F.* X(i,:).*spiral*rand;
                    else
                        % Levy飞行
                        G = 2*(sign(rand-0.5))*(1-Iter/Max_iter);
                        beta = 1.5;
                        sigma = (gamma(1+beta)*sin(pi*beta/2)/(gamma((1+beta)/2)*beta*2^((beta-1)/2)))^(1/beta);
                        u = randn(1, 2*predicted_steps)*sigma;
                        v = randn(1, 2*predicted_steps);
                        step = u./abs(v).^(1/beta);
                        Xnew(i,:) = Position + F.* G*step.* (Position - X(i,:));
                    end
                end
            end
        end
        
        for i = 1:N
            % 连续解的离散化
            Xnew_discrete = zeros(1, 2*predicted_steps);
            for step = 1:predicted_steps
                step_decision_cont = Xnew(i, (2*(step-1)+1) : (2*step) );
                distances = sum((fes_act_dec - step_decision_cont).^2, 2);
                [~, min_idx] = min(distances);
                Xnew_discrete( (2*(step-1)+1) : (2*step) ) = fes_act_dec(min_idx, :);
            end
            
            % 评估新解
            newPopfit = fitness_func(Xnew_discrete);
            
            % 贪心选择
            if newPopfit > fitness(1, i)
                X(i, :) = Xnew_discrete;
                fitness(1, i) = newPopfit;
            end
            
            % 更新全局最优
            if fitness(1, i) > Score
                Position = X(i, :);
                Score = fitness(1, i);
            end
        end

        hist_best{1}(Iter, :) = Position;
        hist_best{2}(Iter, :) = Score;
        Iter = Iter + 1;
    end
    best_position = Position;
end