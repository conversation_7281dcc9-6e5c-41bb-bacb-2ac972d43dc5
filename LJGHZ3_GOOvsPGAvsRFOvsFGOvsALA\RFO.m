
function [fmin0,fbest,ccurve]=RFO(xx,rupplefoxes,itemax,lb,ub,dim,fobj)
%% Information
% % Rupple Fox Optimizer (RFO)

%  This code is designed for continuous, unconstrained, and single objective function minimization problems.

% Input parameters
% rupplefoxes                       Population size
% itemax                   Maximum number of iterations
% lb                       Lower bound of a a particular problem
% ub                       Unuer bound of a particular problem
% dim:                     Number of decision variables
% fobj:                    Objective function to be minimized or maximized

% Output parameters

% fmin0                    Cost of global optimum solution
% fbest                    Global optimum solution
% ccurve                   Convergence curve obtained in solving an optimization problem

%% Convergence curve
ccurve=zeros(1,itemax);
if size(ub,1)==1
    ub=ones(1,dim).*ub;
    lb=ones(1,dim).*lb;
end


%% Start RFO
% Initial population
fposition=initialization(rupplefoxes,dim,ub,lb); % Generation of initial solutions (position of rupple foxes)

%% Evaluate the initial fitness (fitness of the initial population)
fPos = xx;
fit = zeros(rupplefoxes,1);
for i=1:size(fposition,1)
    fit(i)=fobj(fposition(i,:));
end
%% Initalize the parameters of RFO
fitness=fit; % Initial fitness of the random position of the rupple foxes
[fmin0,index]=min(fit);
pbest = fposition; % Initialization of the best positions
fbest = pbest(index,:); % initial global position
x=pbest;

L=100;
% beta=1*10^-10;  %constant for walk rate update; this factor limits the step sizes of random walks

vec_flag=[1,-1];

%% Start RFO
for ite=1:itemax
    beta = 0.3 * (1 - ite/itemax);  % 随迭代递减
    h=1/(1+exp((ite-itemax/2)/L)) ; % eyesight  decreasing (daylight) 1--0 up to night
    s=1/(1+exp((itemax/2 - ite)/L)) ; % % hear increasing (daylight) 0--1 (up to night)
    smell=(0.1/abs(acos((2/(1+exp((itemax/2 - ite)/100)))))); % up and down (variation)

    %% Eyesight and hearing behaviors - at daylight and night
    % Daylight
    if (rand>=0.5) % daylight
        for i=1:size(fposition,1)
            ds=ceil(rupplefoxes*rand(1,rupplefoxes));
            if s>=h % % eyesight > hear  % based on eyes
                if rand>=0.25 %first phase (daylight sight is better) % exploration

                    X_randm = pbest(ds(i),:);
                    rand_index = floor(4*rand()+1)*rand;

                    fposition(i,:)= 1*x(i,:) +rand_index*(X_randm - x(i,:))*1 +rand_index*(fbest-x(i,:))*1; % Exploitation

                    theta = (rand*260*2*pi/360);
                    fposition(i,:) = frotate(fposition(i,:),X_randm, theta, dim); % Exploration

                else % second phase
                    flag_index = floor(2*rand()+1);
                    Flag=vec_flag(flag_index);
                    X_randm = pbest(ds(i),:);
                    fposition(i,:)=X_randm+1*beta*randn(1,dim)*Flag;% The step factor limits the step sizes of random walks % Exploration
                end

            else % hear > eysight

                if (rand >=0.75) %daylight
                    X_randm = pbest(ds(i),:);
                    rand_index = floor(4*rand()+1)*rand;%

                    fposition(i,:)= 1*x(i,:) +rand_index*(X_randm - x(i,:))*1 +rand_index*(fbest-x(i,:))*1;

                    theta = (rand*150*pi/180);
                    fposition(i,:) = frotate(fposition(i,:),X_randm, theta, dim);

                else % second phase
                    flag_index = floor(2*rand()+1);
                    Flag=vec_flag(flag_index);
                    fposition(i,:)=pbest(ds(i),:)+1*beta*randn(1,dim)*Flag;% The beta factor limits the step sizes of random walks % Exploration

                end

            end

        end
    else
        % Night
        for i=1:size(fposition,1) % night
            dr=floor(rupplefoxes*rand(1,rupplefoxes))+1;
            if h>=s %
                if rand>=0.25 %first phase % exploration
                    X_randm = pbest(dr(i),:);
                    rand_index = floor(4*rand()+1)*rand;%

                    fposition(i,:)= 1*x(i,:) +rand_index*(X_randm - x(i,:))*1 +rand_index*(fbest-x(i,:))*1; % Expolitation

                    theta =( rand*150*pi/360);
                    fposition(i,:) = frotate(fposition(i,:),X_randm, theta, dim); % Exploration

                else % second phase

                    flag_index = floor(2*rand()+1);
                    Flag=vec_flag(flag_index);

                    fposition(i,:)=pbest(dr(i),:)+1*beta*randn(1,dim)*Flag;% The beta factor limits the step sizes of random walks % Exploration
                end
            else % hear < eysight

                if (rand >=0.75) %daylight
                    X_randm = pbest(dr(i),:);
                    rand_index = floor(4*rand()+1)*rand;%


                    fposition(i,:)= 1*x(i,:) +rand_index*(X_randm - x(i,:))*1 +rand_index*(fbest-x(i,:))*1;

                    theta = (rand*260*2*pi/180);
                    fposition(i,:) = frotate(fposition(i,:),X_randm, theta, dim);

                else % second phase

                    flag_index = floor(2*rand()+1);

                    Flag=vec_flag(flag_index);

                    fposition(i,:)=pbest(dr(i),:)+1*beta*randn(1,dim)*Flag;% The beta factor limits the step sizes of random walks % Exploration

                end

            end
        end
    end

    %% Smell behavior
    % 
    % for i=1:size(fposition,1)
    %     %Exploitation Phase (Food Exists)
    %     if rand>=smell
    %         ss=floor(1*rand(1,rupplefoxes))+1;
    %         X_randm1 = pbest(ss(i),:);
    %         xmin=2; xmax=4;
    %         xr=xmin+rand()*(xmax-xmin);
    %         eps=abs((4*rand()) - (1*rand()+rand()))/xr;
    % 
    %         fposition(i,:)= x(i,:) +1*(X_randm1-x(i,:))*eps + 1*eps*(fbest - x(i,:));
    % 
    %     else
    % 
    %         flag_index = floor(2*rand()+1);
    % 
    %         Flag=vec_flag(flag_index);
    %         ss=floor(1*rand(1,rupplefoxes))+1;
    %         fposition(i,:)=pbest(ss(i),:)+beta*randn(1,dim)*Flag;% The factor 0.001 limits the step sizes of random walks
    %     end
    % end

    %% Animal behavior
    for i=1:size(fposition,1)
        if rand>=h
            ab=floor(rupplefoxes*rand(1,rupplefoxes))+1;
            fposition(i,:)=pbest(ab(i),:)+beta*randn(1,dim)*1;

        else
            ab=floor(rupplefoxes*rand(1,rupplefoxes))+1;
            X_randp = pbest(ab(i),:);
            Dista=2*(fbest -fposition(i,:))*rand;
            Distb=3.0*(X_randp-pbest(i,:))*rand;
            flag_index = floor(2*rand()+1);
            Flag=vec_flag(flag_index);
            if(i==1)
                fposition(i,:)=fposition(i,:)+1*Dista*1+Distb*Flag;
            else
                fPos(i,:)= fposition(i,:)+1*Dista*1+Distb*Flag;
                fposition(i,:)=(fPos(i,:)+fposition(i-1,:))/(2);
            end
        end
    end

    %% Worst Case Exploration
    for i=1:size(fposition,1)
        flag_index = floor(2*rand()+1);
        fox=vec_flag(flag_index);
        if fox==1
            [~, gworst] = max(fitness);
            fposition(gworst,:)=fposition(gworst,:) + beta*randn(1,dim);
        end
    end


    %%  Handling boundary violations
    for i=1:size(fposition,1)
        Flag4ub=fposition(i,:)>ub;
        Flag4lb=fposition(i,:)<lb;
        fposition(i,:)=(fposition(i,:).*(~(Flag4ub+Flag4lb)))+ub.*Flag4ub+lb.*Flag4lb;
    end


    %% Update global, best and new position
    for i=1:size(fposition,1)

        fit(i)=fobj(fposition(i,:)); % calculate the objective function
        % Update if the solution improves
        if (fit(i)<fitness(i))
            pbest(i,:) = fposition(i,:); % Update the best fposition
            fitness(i)=fit(i); % Update the fitness
        end
        % % Updating fbest and best fitness

        if (fitness(i)<fmin0)
            fmin0=fitness(i);
            fbest = pbest(i,:); % Update the global best positions
        end
    end
    % outmsg = ['Iteration# ', num2str(ite) , '  Fitness= ' , num2str(fmin0)];
    % disp(outmsg);
    ccurve(ite)=fmin0; % Best found value until iteration ite

end

end



%微信公众号搜索：淘个代码，获取更多免费代码
%禁止倒卖转售，违者必究！！！！！
%唯一官方店铺：https://mbd.pub/o/author-amqYmHBs/work
%代码清单：https://docs.qq.com/sheet/DU3NjYkF5TWdFUnpu
