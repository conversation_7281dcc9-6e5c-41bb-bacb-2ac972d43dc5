% 决策算法基类
% 公告状态：各类环境信息，目标概率矩阵，吸引信息素矩阵，排斥信息素矩阵，优化算法类
% 保护状态：目标概率更新参数，吸引信息素更新参数，排斥信息素更新参数
classdef decision_Algorithm < handle
    % 属性
    properties
        targetDstMap; % 目标概率密度矩阵
        ApheromMap; % 吸引信息素矩阵      
        RpheromMap; % 排斥信息素矩阵


        tar_num; % 已搜索到目标
    end

    properties (Access = protected)
        delta_0; % 目标分布初始方差
        delta_e; % 目标维纳过程预测方差
        decmake_time; % 决策间隔
        lmd_tar_atr; % 目标概率对吸引信息素影响因子
        
        Ea; Ga; da; % 吸引信息素演化参数
        Er; Gr; dr; % 排斥信息素演化参数
        T_aph_switch; % 目标概率影响判断,防止过度搜索，即只有访问间隔大于一定时长，才考虑目标存在性对智能体的吸引程度

        gridSize;
        gridDims;
    end

    methods
        function self = decision_Algorithm(delta_0, delta_e, decmake_time, lmd_tar_atr, Ea, Ga, da, Er, Gr, dr, T_aph_switch, areaSize, gridSize)
            self.delta_0 = delta_0;
            self.delta_e = delta_e;
            self.decmake_time = decmake_time;
            self.lmd_tar_atr = lmd_tar_atr;
            self.Ea = Ea;
            self.Ga = Ga;
            self.da = da;
            self.Er = Er;
            self.Gr= Gr;
            self.dr= dr;

            self.T_aph_switch = T_aph_switch;            

            self.gridSize = gridSize;
            self.gridDims = areaSize / gridSize;

            self.targetDstMap = 1 / (areaSize(1) * areaSize(2) * gridSize^2) * ones(self.gridDims); % 目标概率密度矩阵     
            self.ApheromMap = zeros(self.gridDims); % 吸引信息素矩阵
            self.RpheromMap = zeros(self.gridDims); % 排斥信息素矩阵

            self.tar_num = []; % 已观测目标
        end

        function decision =  get_decision(self, agent_list, targetPos, T_last, Last_visit_time, step)
            decision = [];
            % 探测更新目标概率矩阵
            for i_agent = 1: size(agent_list, 2)
                for i_ob =  1:size(agent_list(i_agent).ob_area, 1)
                    if ismember(agent_list(i_agent).ob_area(i_ob,:), targetPos, "rows") && ...
                        (isempty(self.tar_num) || ~ismember(agent_list(i_agent).ob_area(i_ob,:), self.tar_num, "rows"))

                        self.tar_num = [self.tar_num; agent_list(i_agent).ob_area(i_ob,:)];
                        self.targetDstMap = max( self.targetDstMap, self.dctTargetMap(agent_list(i_agent).ob_area(i_ob,:)) );
                        self.targetDstMap = self.targetDstMap ./ sum(sum(self.targetDstMap));
                    end
                end            
            end

            self.targetDstMap = self.preTargetMap();
            
            [self.ApheromMap, self.RpheromMap] = self.updata_pheromMap(T_last, Last_visit_time, step);

            for i_agent = 1: size(agent_list, 2)
                decision = [decision; self.get_best_next_position(agent_list(i_agent).optional_position)];
            end
        end
    end

    methods (Access = protected)
        function best_next_position = get_best_next_position(self, optional_position)
            % 待改进，优化算法
            %     best_next_position =  optional_position(randi([1, size(optional_position, 1)]), :);
            pheromMap = self.ApheromMap - self.RpheromMap;
            best_position = []; 
            best_value =  -realmax('double');
            for i_op = 1:size(optional_position{1}, 1)
                if pheromMap(optional_position{1}(i_op, 1), optional_position{1}(i_op, 2)) > best_value
                    best_value = pheromMap(optional_position{1}(i_op, 1), optional_position{1}(i_op, 2));
                    best_position = optional_position{1}(i_op, :);
                end
            end
            best_next_position = best_position;
        end

        function P_target = dctTargetMap(self, targetPos)
            % 探索目标概率图
            P_target = zeros(self.gridDims);
            x_idx = targetPos(1);
            y_idx = targetPos(2);
            for x = 1:self.gridDims(1)
                for y = 1:self.gridDims(2)
                    dist = norm([x,y] - [x_idx, y_idx]);
                    P_target(x,y) = P_target(x,y) + (1/(2*pi*self.delta_0^2)) * exp(-dist^2/(2*self.delta_0^2));
                end
            end
            P_target = P_target / sum(P_target(:));
        end

        function preTargetMap = preTargetMap(self)
            preTargetMap = zeros(self.gridDims);

            f_integral = zeros(self.gridDims); % 待积分矩阵f_k_k1*f_k1

            % 求解目标概率密度
            for ix = 1:self.gridDims(1)
                for iy = 1:self.gridDims(2)
                    %求解带积分矩阵，记各栅格对于(ix,iy)栅格目标存在概率的作用分量
                    for fx = 1:self.gridDims(1)
                        for fy = 1:self.gridDims(2)
                            f_k_k1 = (1/(2 * pi * self.delta_e^2 * self.decmake_time)) * exp(-(((ix - fx))^2 + ((iy - fy))^2) / (2 * self.delta_e ^ 2 * self.decmake_time));
                            f_integral(fx, fy) = f_k_k1 * self.targetDstMap(fx, fy);
                        end
                    end
                    preTargetMap(ix, iy) = self.double_integral(f_integral, self.gridDims(1), self.gridDims(2)); % 更新概率密度矩阵
                end
            end
            % 求解目标概率密度
            preTargetMap =  preTargetMap ./ sum(sum(preTargetMap));
        end

        function [AphMap, RphMap] = updata_pheromMap(self, T_last, Last_visit_time, step)
            % 更新数字信息素图(DPM) - 包括吸引和排斥信息素
            AphMap = zeros(self.gridDims);
            RphMap = zeros(self.gridDims);
            CK = zeros(self.gridDims); % 吸引度释放开关

            for i = 1:self.gridDims(1)
                for j = 1:self.gridDims(2)
                    % 计算吸引信息素开关系数
                    if Last_visit_time(i, j) == 0
                        CK(i, j) = 1;
                    else
                        if step - Last_visit_time(i, j) > self.T_aph_switch
                            CK(i, j) = 1;
                        else
                            CK(i, j) = 0;
                        end
                    end
                    % 信息素传播
                    neighbors = self.getNeighbors(i, j);
                    GP_a = 0;
                    GP_r = 0;
                    for n = 1:size(neighbors, 1)
                        ni = neighbors(n, 1);
                        nj = neighbors(n, 2);
                        GP_a = GP_a + self.Ga * (self.ApheromMap(ni, nj) + self.da) / size(neighbors, 1);
                        GP_r = GP_r + self.Gr * (self.RpheromMap(ni, nj) + self.dr) / size(neighbors, 1);
                    end
                    % 更新吸引信息素
                    if T_last(i, j) == 0
                        AphMap(i, j) =  (1-self.Ea) * ( (1-self.Ga) * (self.ApheromMap(i,j) + CK(i,j) * self.da * self.lmd_tar_atr * self.targetDstMap(i,j) ) + GP_a);
                    end
                    % 更新排斥信息素
                    RphMap(i, j) = (1-self.Er) * ( (1-self.Gr) * (self.RpheromMap(i,j) + T_last(i,j) * self.da ) + GP_r);
                end
            end
        end

        function neighbors = getNeighbors(self, i, j)
            % 获取相邻栅格，网格范围gridSize
            neighbors = [];
            for di = -1:1
                for dj = -1:1
                    if di == 0 && dj == 0
                        continue;
                    end
                    ni = i + di;
                    nj = j + dj;
                    if ni >= 1 && ni <= self.gridDims(1) && nj >= 1 && nj <= self.gridDims(2)
                        neighbors = [neighbors; ni, nj];
                    end
                end
            end
        end

        function result = double_integral(self, f, x, y)
            [rows, cols] = size(f);
            if x < 1 || x > rows || y < 1 || y > cols
                error('x和y必须在栅格范围内');
            end
            % 初始化积分结果
            result = 0;
            % 计算二维积分（累加求和）
            for i = 1:x
                for j = 1:y
                    result = result + f(i,j) * self.gridSize^2;
                end
            end
        end

    end

end