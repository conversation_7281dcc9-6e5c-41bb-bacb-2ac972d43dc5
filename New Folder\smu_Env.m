% 环境算法基类：
% 公告状态：智能体数量，地图范围，目标位置，决策时间，发现目标坐标，上一时刻访问状态矩阵，栅格最晚被访问时间矩阵
% 公告方法：初始化，更新，
classdef smu_Env < handle
    % 属性
    properties
        T_last; % 当前时刻访问状态矩阵
        Last_visit_time; % 上次被访问时间矩阵
        search_Map; % 覆盖矩阵
        step; % 当前步数

        decision; % 决策动作
        agent_list; % 智能体列表
        search_AL; % 搜索算法

        targetPos;
    end

    properties (Access = protected)
        areaSize; % 地图范围
        gridSize; % 栅格尺寸
        gridDims

        step_max; % 最大步数
        agent_size; % 智能体数量

        delta_0;              % 初始方差
        delta_e;             % 类型二目标维纳过程方差
        decmake_time;
        predicted_steps;
        lmd_tar_atr;

        Ea; % 信息素参数
        Ga;
        da;
        Er;
        Gr;
        dr;
        T_aph_switch;
    end

    methods
        function self = smu_Env(areaSize, gridSize, step_max, agent_size, targetPos, agent_initial, agent_property, predicted_steps)
            self.areaSize = areaSize;
            self.gridSize = gridSize;
            self.gridDims = areaSize / gridSize;
            self.step_max = step_max;
            self.step = 1;

            self.agent_size = agent_size;

            self.targetPos = targetPos;

            self.T_last = zeros(self.gridDims); 
            self.Last_visit_time = zeros(self.gridDims);
            self.search_Map = zeros(self.gridDims);

            self.delta_0 = 3;              % 初始方差
            self.delta_e = 0.8;             % 类型二目标维纳过程方差
            self.decmake_time = 10;
            self.predicted_steps = predicted_steps;
            self.lmd_tar_atr = 100;

            self.Ea = 0.4; self.Ga = 0.4; self.da = 3;
            self.Er = 0.4; self.Gr = 0.4; self.dr = 2;
            self.T_aph_switch = 10;
            
            self.decision = [];
            self.agent_list = [];
            for i_ag = 1:self.agent_size
                initial_position = agent_initial(i_ag, 1:2);
                initial_position = floor(initial_position ./ self.gridSize) + 1;
                initial_position = max([1,1], min(initial_position, self.gridDims));

                initial_speed = agent_initial(i_ag, 3);
                initial_heading = agent_initial(i_ag, 4);

                speed_max = agent_property(i_ag, 1);
                dt_heading_max = agent_property(i_ag, 2);
                Ob_radius_max = agent_property(i_ag, 3);

                self.agent_list = [self.agent_list,...  
                    Agent(initial_position, initial_speed, initial_heading, speed_max, dt_heading_max, Ob_radius_max, predicted_steps, self.areaSize, self.gridSize, self.decmake_time)];
            end


            self.search_AL = decision_Algorithm(self.delta_0, self.delta_e, self.decmake_time, self.lmd_tar_atr, self.Ea, self.Ga, self.da, ...
                                                self.Er, self.Gr, self.dr, self.T_aph_switch, self.areaSize, self.gridSize);
        end

        % 运行，调用入口
        function run(self)
            for loop = 1:self.step_max
                self.step = loop;
                tic % 启动一个秒表计时器（MATLAB 内置函数）
                self.update();
                toc  % 关闭一个秒表计时器（MATLAB 内置函数），并返回运行时间
                disp(['运行时间: ',num2str(toc)]);

                figure(1)
                clf;
                [X, Y] = meshgrid(1:self.gridDims(1), 1:self.gridDims(2)); % Create meshgrid for x, y coordinates
                mesh(X, Y, self.search_AL.targetDstMap');
                xlabel('x/km');
                ylabel('y/km');
                zlabel('Probability');  % Add a z-axis label for probability
                view(3); % Enable 3D view
                hold on

                figure(4); clf;
%                 [X, Y] = meshgrid(1:self.gridDims(1), 1:self.gridDims(2)); % Create meshgrid for x, y coordinates
                mesh(X, Y, self.search_AL.ApheromMap' - self.search_AL.RpheromMap');
                xlabel('x/km');
                ylabel('y/km');
                zlabel('Probability');  % Add a z-axis label for probability
                view(3); % Enable 3D view
                hold on

%                 disp(['覆盖率: ', num2str(sum(self.search_Map(:)) / (self.gridDims(1) * self.gridDims(2)) ) ])
            end
        end

        function self = update(self)
            self = self.visit_updata();
            self.decision = self.search_AL.get_decision(self.agent_list, self.targetPos, self.T_last, self.Last_visit_time, self.step);
            for i_agent =  1: size(self.agent_list, 2)
                self.agent_list(i_agent).agent_update(self.decision(i_agent,:), self.areaSize, self.gridSize, self.decmake_time);
            end
        end
    end

    methods (Access = protected)
        
        function self = visit_updata(self)
            self.T_last = zeros(self.gridDims);
            % 搜索环境信息
            for i_agent = 1: size(self.agent_list, 2)
                for i_ob =  1:size(self.agent_list(i_agent).ob_area, 1)
                    self.T_last(self.agent_list(i_agent).ob_area(i_ob,1), self.agent_list(i_agent).ob_area(i_ob,2)) = 1;                    
                    self.Last_visit_time(self.agent_list(i_agent).ob_area(i_ob,1), self.agent_list(i_agent).ob_area(i_ob,2)) = self.step;
                    self.search_Map = max(self.search_Map, self.T_last);
                end
            end
        end
    end


end