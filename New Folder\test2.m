%% 清理之前的数据
% 清除所有数据
clear all;
close all;
% 清除窗口输出
clc;

%% 添加目录
% 将上级目录中的frame文件夹加入路径
addpath('../frame')

lmd_tar_atr = 10;
Ea = 0.4; 
Ga = 0.4;
da = 3;
Er = 0.4; 
Gr = 0.4;
dr = 2;
areaSize = [1000 900];  % 区域范围
gridSize =  20;          % 栅格尺寸1km
delta_0 = 3;              % 初始方差
delta_e = 0.8;             % 类型二目标维纳过程方差
decmake_time = 10;
T_aph_switch = 10;

targetPos = [10 10;
             10 20;
             10 30;
             10 40;
             20 10;
             20 20;
             20 30;
             20 40;
             30 10;
             30 20;
             30 30;
             30 40;
             40 10;
             40 20;
             40 30;
             40 40];  

agent_initial = [  0, 750, 0,   pi/4;
                   0, 250, 0,  -pi/4];
agent_property = [10, pi/4, 50;
                  10, pi/4, 50];


Sm = smu_Env(areaSize, gridSize, 80, 2, targetPos, agent_initial, agent_property, 1);

Sm.run();

% figure(10)
% for i= 1:size(Sm.agent_list, 2)
%     plot((Sm.agent_list(i).path(:,1) - 0.5) * gridSize, ...
%         (Sm.agent_list(i).path(:,2) - 0.5) * gridSize,  'LineWidth', 2);
%     hold on
% end

figure(11)
for i= 1:size(Sm.agent_list, 2)
    plot((Sm.agent_list(i).position_history(:,1) - 0.5) * gridSize, ...
         (Sm.agent_list(i).position_history(:,2) - 0.5) * gridSize,  'LineWidth', 2);
    hold on
end

% 
% tic % 启动一个秒表计时器（MATLAB 内置函数
% decision =  dA.get_decision(agent_list, targetPos, T_last, Last_visit_time, 50);
% toc  % 关闭一个秒表计时器（MATLAB 内置函数），并返回运行时间
% disp(['运行时间: ',num2str(toc)]);


