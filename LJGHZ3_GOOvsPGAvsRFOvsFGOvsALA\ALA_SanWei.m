function [bestY,bestX,recording]=ALA_SanWei(x,y,option,data)
    SearchAgents_no=option.numAgent;
    Max_iter=option.maxIteration;
    lb=option.lb;
    ub=option.ub;
    dim=option.dim;
    fobj=option.fobj;
    [Alpha_score,Alpha_pos,Convergence_curve]=ALA(x,SearchAgents_no,Max_iter,lb,ub,dim,fobj);
    %% 初始化
    recording.bestFit=[min(y),Convergence_curve]';
    recording.meanFit=zeros(option.maxIteration+1,1);
    bestY=Alpha_score;
    bestX=Alpha_pos;
end
