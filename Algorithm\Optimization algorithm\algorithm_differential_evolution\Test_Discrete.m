%% 清理数据
clear all; 
clc;
addpath('../frame')

%% 定义离散二维函数

% 创建离散点网格 (保持不变)
x_range = -4:0.5:4;
y_range = -4:0.5:4;
[X, Y] = meshgrid(x_range, y_range);

% 计算每个点的函数值Z
Z = X.^2 + Y.^2;

% 创建离散点集合
discrete_points = [X(:),Y(:)];

%% 寻找真实最优解
% 理论最优解
[value,idx] = min(Z(:));
true_optimal_value = value;
true_optimal_pos = [X(idx),Y(idx)];

fprintf('理论最优解为: %.4f, 位于 (%.2f, %.2f)\n\n', true_optimal_value, true_optimal_pos(1), true_optimal_pos(2));

%% 初始化算法

dim = 2;
size_pop = 50;
iter_max = 10;

% 实例化离散DE算法对象
de_discrete_algorithm = DE_Base_Discrete(dim,size_pop,iter_max,discrete_points);
de_discrete_algorithm.is_cal_max = false;

% 设置适应度函数
de_discrete_algorithm.fitfunction = @(pos) pos(1)^2 + pos(2)^2;

% 运行算法
de_discrete_algorithm.run();

%% 结果分析
fprintf('结果分析：\n');
% 获取最优值，需要考虑是否为求最大值
if de_discrete_algorithm.is_cal_max
    best_value = de_discrete_algorithm.value_best;
else
    best_value = -de_discrete_algorithm.value_best;
end
fprintf('算法找到的最优值: %.4f\n', best_value);
fprintf('算法找到的最优位置: (%.2f, %.2f)\n', de_discrete_algorithm.position_best(1), de_discrete_algorithm.position_best(2));

%% 可视化
% 绘制三维图
figure(1)
surf(X,Y,Z);
hold on
plot3(true_optimal_pos(1),true_optimal_pos(2),true_optimal_value,'go','MarkerSize',10,'LineWidth',2);
plot3(de_discrete_algorithm.position_best(1),de_discrete_algorithm.position_best(2),best_value,'rx','MarkerSize',12,'LineWidth',2);
title('球面函数与最优点');
xlabel('x');
ylabel('y');
zlabel('z');
legend('函数曲面','真实最优解','算法找到的最优解','Location','northeast');

% 绘制等高线图
figure(2)
contour(X,Y,Z);
hold on;
plot(true_optimal_pos(1),true_optimal_pos(2),'go','MarkerSize',10,'LineWidth',2);
plot(de_discrete_algorithm.position_best(1),de_discrete_algorithm.position_best(2),'rx','MarkerSize',12,'LineWidth',2);
title('函数等高线与最优点');
xlabel('x');
ylabel('y');
zlabel('z');
legend('等高线','真实最优解','算法找到的最优解','Location','northeast');

% 绘制收敛曲线
figure(3)
plot(1:iter_max,de_discrete_algorithm.value_best_history,'b-','LineWidth',2)
title('算法收敛曲线')
xlabel('迭代次数')
ylabel('每代最优值')
grid on





