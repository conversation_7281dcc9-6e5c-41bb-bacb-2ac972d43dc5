import numpy as np
import math
def ALA_optimization(fes_act_dec, fitness_func, predicted_steps):    
    # 算法参数设置
    N = min(15, fes_act_dec.shape[0])  # 种群的大小
    Max_iter = 15  # 最大迭代次数
    vec_flag = np.array([1, -1])  # 方向标志向量
    hist_best = [np.zeros((Max_iter, 2*predicted_steps)), np.zeros(Max_iter)]  # 历史最优解和适应度记录
    # 初始化种群
    X = np.zeros((N,2*predicted_steps))
    fitness = np.zeros(N)
    for i in range(N):
        decision_vector = []
        for step in range(predicted_steps):
            idx = np.random.randint(fes_act_dec.shape[0])
            decision_vector.append(fes_act_dec[idx, :])
        X[i, :] = np.concatenate(decision_vector)
        fitness[i] = fitness_func(X[i, :])    
    # 找到初始最优解
    Position = np.zeros((2*predicted_steps))
    Score = -np.inf
    for i in range(N):
        if fitness[i] > Score:
            Position = X[i, :]
            Score = fitness[i]
    Iter = 1
    # 算法主循环
    while Iter <= Max_iter:
        RB = np.random.randn(N, 2*predicted_steps)  # 布朗运动
        F = vec_flag[np.random.randint(2)]  # 随机方向标志
        theta = 2*np.atan(1 - Iter/Max_iter)  # 时变参数
        p_cauchy = 1 - (Iter / Max_iter) # 柯西变异概率
        p_guassian = Iter / Max_iter # 高斯变异概率
        # 位置更新阶段
        Xnew = np.zeros((N,2*predicted_steps))
        for i in range(N):
            E = 2*np.log(1/np.random.rand())*theta
            if E > 1: # 高能量状态
                r = np.random.rand()
                # 柯西变异
                if r < p_cauchy and r > 0.3:
                    cauchy_step = 1.0 - (Iter / Max_iter)
                    cauchy_mutate = cauchy_step * np.tan(np.pi * (np.random.rand(1, 2*predicted_steps) - 0.5))
                    Xnew[i, :] = Position + cauchy_mutate
                elif r <= 0.3:
                    # 群体协作
                    r1 = 2*np.random.rand(1, 2*predicted_steps) - 1
                    Xnew[i,:] = Position + F*RB[i,:]*(r1*(Position-X[i,:])+(1-r1)*(X[i,:]-X[np.random.randint(N),:]))
                else: 
                    # 定向搜索
                    r2 = np.random.rand() * (1 + np.sin(0.5 * Iter))
                    Xnew[i,:] = X[i,:] + F* r2*(Position-X[np.random.randint(N),:])
            else:
                r = np.random.rand()
                if r < p_guassian and r > 0.5:
                    guassian_step = 1 - (Iter/Max_iter)
                    guassian_mutate = guassian_step*np.random.randn(1,2*predicted_steps)
                    Xnew[i, :] = Position + guassian_mutate
                elif r <= 0.5:
                    # 螺旋搜索
                    radius = np.sqrt(np.sum((Position-X[i, :])**2))
                    r3 = np.random.rand()
                    spiral = radius*(np.sin(2*np.pi*r3)+np.cos(2*np.pi*r3))
                    Xnew[i,:] = Position + F* X[i,:]*spiral*np.random.rand()
                else:
                    # 莱维飞行
                    G = 2*(np.sign(np.random.rand() - 0.5))*(1-Iter/Max_iter)
                    beta = 1.5
                    sigma = (math.gamma(1+beta)*np.sin(np.pi*beta/2)/(np.gamma((1+beta)/2)*beta*2**((beta-1)/2)))**(1/beta)
                    u = np.random.randn(1, 2*predicted_steps)*sigma
                    v = np.random.randn(1, 2*predicted_steps)
                    step = u/np.abs(v)**(1/beta)
                    Xnew[i,:] = Position + F* G*step*(Position - X[i,:])        
            # 连续解的离散化
            Xnew_discrete = np.zeros((N, 2*predicted_steps))
            for i in range(N):
                for step in range(predicted_steps):
                    start_idx = 2 * step
                    end_idx = 2 * step + 2
                    step_decision_cont = Xnew[i, start_idx:end_idx]
                    distance = np.sum((fes_act_dec - step_decision_cont)**2, axis=1)
                    min_idx = np.argmin(distance)
                    Xnew_discrete[i, start_idx:end_idx] = fes_act_dec[min_idx, :]
            
            # 对每个个体分别评估
            newPopfit = fitness_func(Xnew_discrete[i, :])
            # 贪心选择
            if newPopfit > fitness[i]:
                X[i, :] = Xnew_discrete[i, :]
                fitness[i] = newPopfit
            # 更新全局最优
            if fitness[i] > Score:
                Position = X[i, :]
                Score = fitness[i]
        hist_best[0][Iter-1, :] = Position
        hist_best[1][Iter-1] = Score
        Iter += 1
    best_position = Position
    return best_position, hist_best
