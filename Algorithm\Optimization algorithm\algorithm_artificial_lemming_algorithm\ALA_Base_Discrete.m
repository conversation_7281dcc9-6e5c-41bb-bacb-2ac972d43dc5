% 离散人工旅鼠算法
classdef ALA_Base_Discrete < ALA_Base
    
    properties
        discrete_points; % 离散点集
    end
    
    methods
        function self = ALA_Base_Discrete(dim, size, iter_max, discrete_points)
            % 从离散点计算范围
            range_min = min(discrete_points, [], 1);
            range_max = max(discrete_points, [], 1);
            
            self@ALA_Base(dim, size, iter_max, range_min, range_max);
            self.discrete_points = discrete_points;
            self.name = 'ALA_Base_Discrete';
        end
    end
    
    methods (Access = protected)
        % 初始化
        function init(self)
            init@Algorithm_Impl(self)
            for i = 1:self.size 
                unit = ALA_Unit();
                % 从离散点中随机选择
                idx = randi(size(self.discrete_points, 1));
                unit.position = self.discrete_points(idx, :);
                unit.value = self.cal_fitfunction(unit.position);
                self.unit_list = [self.unit_list, unit];
            end
        end
        
        % ALA算法核心更新逻辑（离散版本）
        function ala_update(self, iter)
            % 布朗运动
            RB = randn(self.size, self.dim);
            % 随机方向标志
            F = self.vec_flag(floor(2*rand()+1));
            % 时变参数
            theta = 2*atan(1-iter/self.iter_max);
            
            % 为每个个体生成新位置
            for i = 1:self.size
                E = 2*log(1/rand)*theta;
                
                if E > 1
                    if rand < 0.3
                        % 第一种更新策略
                        r1 = 2 * rand(1,self.dim) - 1;
                        new_pos = self.position_best + F.*RB(i,:).*(r1.*(self.position_best-self.unit_list(i).position)+(1-r1).*(self.unit_list(i).position-self.unit_list(randi(self.size)).position));
                    else
                        % 第二种更新策略
                        r2 = rand() * (1 + sin(0.5 * iter));
                        new_pos = self.unit_list(i).position + F.* r2*(self.position_best-self.unit_list(randi(self.size)).position);
                    end
                else
                    if rand < 0.5
                        % 螺旋更新策略
                        radius = sqrt(sum((self.position_best-self.unit_list(i).position).^2));
                        r3 = rand();
                        spiral = radius*(sin(2*pi*r3)+cos(2*pi*r3));
                        new_pos = self.position_best + F.* self.unit_list(i).position.*spiral*rand;
                    else
                        % Levy飞行策略
                        G = 2*(sign(rand-0.5))*(1-iter/self.iter_max);
                        new_pos = self.position_best + F.* G*self.levy_flight(self.dim).* (self.position_best - self.unit_list(i).position);
                    end
                end
                
                % 映射到最近的离散点
                new_pos = self.go_to_discrete(new_pos);
                
                % 评估新解
                new_value = self.cal_fitfunction(new_pos);
                
                % 贪心选择
                if new_value > self.unit_list(i).value
                    self.unit_list(i).position = new_pos;
                    self.unit_list(i).value = new_value;
                end
            end
        end
        
        % 映射到最近的离散点
        function discrete_pos = go_to_discrete(self, continuous_pos)
            distances = sum((self.discrete_points - continuous_pos).^2, 2);
            [~, min_idx] = min(distances);
            discrete_pos = self.discrete_points(min_idx, :);
        end
    end
end
