% 人工旅鼠算法基类
% Artificial lemming algorithm: A novel bionic meta-heuristic technique for solving real-world engineering optimization problems
classdef ALA_Base < Algorithm_Impl
    
    properties
        % 算法名称
        name = 'ALA';
        % 方向标志向量
        vec_flag = [1, -1];
        % 创建一个停滞计数器
        stagnation_counter = 0;
        % 创建停滞计数器阈值
        stagnation_threshold = 20;
        % 触发重置时种群重置比例
        reset_ratio = 0.5;
    end
    
    % 外部可调用的方法
    methods
        function self = ALA_Base(dim,size,iter_max,range_min_list,range_max_list)
            % 调用父类构造函数
            self@Algorithm_Impl(dim,size,iter_max,range_min_list,range_max_list);
        end
        % 重置一部分旅鼠，增加随机性
        function reset_lemming(self)
            num_reset = floor(self.size * self.reset_ratio);
            % 随机选择要重置的旅鼠的索引
            indices_to_reset = randperm(self.size,num_reset);
            % 遍历并重置这些旅鼠
            for i = 1:length(indices_to_reset)
                idx = indices_to_reset(i);
                % 随机生成新位置
                self.unit_list(idx).position = unifrnd(self.range_min_list,self.range_max_list);
                % 重新计算适应度
                self.unit_list(idx).value = self.cal_fitfunction(self.unit_list(idx).position);
            end
        end
    end
    
    % 继承重写父类的方法
    methods (Access = protected)
        % 初始化种群
        function init(self)
            init@Algorithm_Impl(self)
            %初始化种群
            for i = 1:self.size 
                unit = ALA_Unit();
                % 随机初始化位置：rand(0,1).*(max-min)+min
                unit.position = unifrnd(self.range_min_list,self.range_max_list);
                % 计算适应度值
                unit.value = self.cal_fitfunction(unit.position);
                % 将个体加入群体数组
                self.unit_list = [self.unit_list,unit];
            end      
        end
     
        % 每一代的更新
        function update(self,iter)
            % 记录更新前的最优值
            last_best_value = self.value_best;

            update@Algorithm_Impl(self,iter)
            % ALA算法主要更新逻辑
            self.ala_update(iter);

            % 检查最优解是否更新
            if iter > 1 && self.value_best <= last_best_value
               self.stagnation_counter = self.stagnation_counter + 1;
            else
                self.stagnation_counter = 0;
            end

            % 达到阈值，开启重置
            if self.stagnation_counter >= self.stagnation_threshold
                self.reset_lemming();
                self.stagnation_counter = 0;
            end
        end
        
        % ALA算法核心更新逻辑
        function ala_update(self, iter)
            % 布朗运动
            RB = randn(self.size, self.dim);
            % 随机方向标志
            F = self.vec_flag(floor(2*rand()+1));
            % 时变参数
            theta = 2*atan(1-iter/self.iter_max);
            
            % 为每个个体生成新位置
            for i = 1:self.size
                E = 2*log(1/rand)*theta;
                
                if E > 1
                    if rand < 0.3
                        % 第一种更新策略
                        r1 = 2 * rand(1,self.dim) - 1;
                        new_pos = self.position_best + F.*RB(i,:).*(r1.*(self.position_best-self.unit_list(i).position)+(1-r1).*(self.unit_list(i).position-self.unit_list(randi(self.size)).position));
                    else
                        % 第二种更新策略
                        r2 = rand() * (1 + sin(0.5 * iter));
                        new_pos = self.unit_list(i).position + F.* r2*(self.position_best-self.unit_list(randi(self.size)).position);
                    end
                else
                    if rand < 0.5
                        % 螺旋更新策略
                        radius = sqrt(sum((self.position_best-self.unit_list(i).position).^2));
                        r3 = rand();
                        spiral = radius*(sin(2*pi*r3)+cos(2*pi*r3));
                        new_pos = self.position_best + F.* self.unit_list(i).position.*spiral*rand;
                    else
                        % Levy飞行策略
                        G = 2*(sign(rand-0.5))*(1-iter/self.iter_max);
                        new_pos = self.position_best + F.* G*self.levy_flight(self.dim).* (self.position_best - self.unit_list(i).position);
                    end
                end
                
                % 边界检查
                new_pos = self.get_out_bound_value(new_pos);
                
                % 评估新解
                new_value = self.cal_fitfunction(new_pos);
                
                % 贪心选择
                if new_value > self.unit_list(i).value
                    self.unit_list(i).position = new_pos;
                    self.unit_list(i).value = new_value;
                end
            end
        end
        
        % Levy飞行函数
        function o = levy_flight(self, d)
            beta = 1.5;
            sigma = (gamma(1+beta)*sin(pi*beta/2)/(gamma((1+beta)/2)*beta*2^((beta-1)/2)))^(1/beta);
            u = randn(1,d)*sigma;
            v = randn(1,d);
            step = u./abs(v).^(1/beta);
            o = step;
        end
    end
end
