% 遗传算法
classdef GA_Base < Algorithm_Impl
    
    properties
        % 交叉率
        cross_rate
        % 变异率
        alter_rate
        % 临时种群列表，用于保存轮盘赌选择的个体
        temp_unit_list
    end
    
    % 外部可调用的方法
    methods
        function self = GA_Base(dim,size,iter_max,range_min_list,range_max_list)
            % 调用父类构造函数设置参数
             self@Algorithm_Impl(dim,size,iter_max,range_min_list,range_max_list);
             % 交叉率
             self.cross_rate = 0.8;
             % 变异率
             self.alter_rate = 0.05;
             
        end
    end 
    
    % 继承重写父类的方法
    methods (Access = protected)
        % 初始化种群
        function init(self)
            init@Algorithm_Impl(self)
            %初始化种群
            for i = 1:self.size 
                unit = GA_Unit();
                % 随机初始化位置：rand(0,1).*(max-min)+min
                unit.position = unifrnd(self.range_min_list,self.range_max_list);
                % 计算初始位置的适应度值
                unit.value = self.cal_fitfunction(unit.position);
                % 将个体加入群体数组
                self.unit_list = [self.unit_list,unit];
                self.temp_unit_list=[self.temp_unit_list,unit];
            end
        end
        
         % 每一代的更新
        function update(self,iter)
            % 调用父类 update
            update@Algorithm_Impl(self,iter)
            
            % 轮盘赌选择个体
            self.roulette();
            
            % 交叉
            self.cross();
            
            % 变异
            self.altered();
            
            % 计算适应度函数
            self.cal_value();
        end
        
        % 变异
        function altered(self)
            % 遍历所有个体
            for i = 1:self.size
                % 如果随机数满足变异条件
                if(unifrnd (0,1) < self.alter_rate)
                    % 随机选择一维
                    cur_dim = unidrnd(self.dim);
                    % 该维变异到解空间内的随机位置
                    rand_pos = unifrnd(self.range_min_list(cur_dim),self.range_max_list(cur_dim));
                    % 保存该变异位置
                    self.unit_list(i).position(cur_dim) = rand_pos;
                end
            end
        end
        
        % 交叉（对应维度交换值）
        function cross(self)
            % 遍历步长为2，相邻两个个体交叉
            for i = 1:2:self.size
                % 如果随机数满足交叉条件
                if (unifrnd(0,1) < self.cross_rate)
                    % 随机选择一维
                    cur_dim = unidrnd(self.dim);
                    % 交换相邻个体的对应位置的值
                    temp = self.unit_list(i).position(cur_dim);
                    self.unit_list(i).position(cur_dim) = self.unit_list(i+1).position(cur_dim);
                    self.unit_list(i+1).position(cur_dim) = temp;
                end
            end
        end
        
        % 轮盘赌选择
        function roulette(self)
            % 获取轮盘赌值
            roulette_rate = self.get_roulette_rate();
            % 群体轮盘赌值之和
            roulette_sum = sum(roulette_rate);
            id = 1;
            for i = 1:self.size
                % 生成随机数
                rand = unifrnd(0, roulette_sum);
                % 随机数落在了哪个id的轮盘区间
                cal_roulette_sum = 0; % 循环计算时需要的临时适应度总和
                for j = 1:self.size
                    cal_roulette_sum = cal_roulette_sum + roulette_rate(j);
                    if (j == self.size)
                        id = self.size;
                        break;
                    elseif(rand < cal_roulette_sum)
                        id = j;
                        break;
                    end
                end
                % 记录所选的个体的位置
                self.temp_unit_list(i).position = self.unit_list(id).position;
            end
            % 保存临时列表中保存的选中个体
            for i = 1:self.size 
                self.unit_list(i).position = self.temp_unit_list(i).position;
            end
        end
        
        % 计算轮盘赌概率
        function rate_list = get_roulette_rate(self)
            rate_list = zeros(1,self.size);
            roulette_value_min = realmax('double');
            % 计算出最小的适应度值
            for i = 1:self.size 
                rate_list(i) = self.unit_list(i).value;
                if (rate_list(i) < roulette_value_min) 
                    roulette_value_min = rate_list(i);
                end
            end
            % 计算出每个个体的轮盘赌数值
            for i = 1:self.size
               rate_list(i) = rate_list(i) - roulette_value_min;
            end
        end
        
        % 计算群体适应度值
        function cal_value(self)
            for i = 1:self.size
                self.unit_list(i).value = self.cal_fitfunction(self.unit_list(i).position);
            end
        end
    end
    
end