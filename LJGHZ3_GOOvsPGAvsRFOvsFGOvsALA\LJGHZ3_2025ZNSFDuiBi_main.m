clc;
clear all;
close all;
warning off;

%% 声明全局变量
global data

%% 固定随机数种子
rng('default')
noRng=1;
rng(noRng);

%% 载入数据
data.map=xlsread("三维地图.xlsx");
data.minF=[10,0,0];
data.maxF=[100,1,100];
data.mu_max=pi/3;    %巡检无人机最大俯仰角
data.beta_max=pi/3; %巡检无人机最大转弯角
%% 构建三维地图
data.mapsize=[100,100];
temp=zeros(data.mapsize);
[p1,p2]=find(temp==0);
data.index=sub2ind(data.mapsize,p1,p2);
data.node=[p1,p2];
%%
%%
t=1:1:100; 
[x,y] =meshgrid(t);%%表示区域网格控制，目地是为了让x,y形成格点矩阵
%% %% 基准地形建模 
h1=sin(y+10)+0.2*sin(x)+0.1*cos(0.6*sqrt(x^2+y^2))+1*cos(y)+1*sin(0.1*sqrt(x^2+y^2))+0.1*cos(y); 
h1=(h1+2*randn(size(h1)))*10;
h1 = imgaussfilt(h1,3);
%% 山峰建模、威胁区域建模 
data.Ob=[40,20,5,15,10;
         20,40,5, 6, 5;
         60,80,5,10,10;
         80,60,5, 6, 5;
         20,50,5,10,10;
         70,20,5, 6, 5; 
         80,20,5, 6, 5; 
         ];
data.numOb=length(data.Ob(:,1));
h=[ 28 21 26 30 30 19 20]; %%威胁区域高度
y0=data.Ob(:,1);
x0=data.Ob(:,2);
xs=2*[ 4 7 4 4 4 4 4];
ys=2*[ 6 6 5 6 6 6 6];
data.Ob=[];
for i=1:length(h)
    data.Ob=[data.Ob;x0(i),y0(i),h(i),xs(i),ys(i)];
end
% data.Ob=[40,20,5,15,10;
%          20,40,5, 6, 5;
%          60,80,5,10,10;
%          80,60,5, 6, 5;
%          20,50,5,10,10;
%          70,20,5, 6, 5;    
%          ];
for x=1:100 
    for y=1:100
        for i=1:7
            h2(i)=h(i)*exp(-((x-x0(i))/xs(i))^2-((y-y0(i))/ys(i))^2); 
            h3(x,y)=sum(h2); 
        end
    end
end
z=max(h1,h3); 
[r1 c1]= size(z); 
x=1:r1; 
y=1:c1; %设置横纵坐标 
data.map_z=z;
%%
%% 
data.minH=10;       %最低高度
data.maxH=20;      %最高高度
data.S=[1,1,10];    %起点【1 1 10】
data.E0=[20,60,10;%60-40
        60,50,10;          %%  [60 50 10]
        100,100,10];  %终点10-20

data.M=760;
data.g=9.8;
data.S(3)=max(data.map_z(data.S(1),data.S(2))+1,data.S(3));
for i=1:length(data.E0(:,1))
    data.E0(i,3)=max(data.map_z(data.E0(i,1),data.E0(i,2))+1,data.E0(i,3));
end
data.map0=z;
data.mapSize0=size(z);
data.color=rand(4,3);
[p1,p2]=find(~isinf(data.map_z));
index=sub2ind(data.mapSize0,p1,p2);
data.node=[p1,p2,z(index),z(index)];
data.numNode=length(p1);
data.D=pdist2(data.node(:,1:2),data.node(:,1:2));
[p1,p2]=find(data.D<=sqrt(2) & data.D>0);
data.net=[p1,p2];
data.noS=find(data.node(:,1)==data.S(1) & data.node(:,2)==data.S(2) );

for i=1:length(data.E0(:,1))
    data.noE0(i)=find(data.node(:,1)==data.E0(i,1) & data.node(:,2)==data.E0(i,2) );
end
%%
data.map_z=z;
data.map_x=x;
data.map_y=y;
data.sizeMap=size(data.map_z);
%%


figure
plot3(data.S(:,1),data.S(:,2),data.S(:,3),'o','LineWidth',2,...
                     'MarkerEdgeColor','k',...
                     'MarkerFaceColor','r',...
                     'MarkerSize',10)
hold on
plot3(data.E0(:,1),data.E0(:,2),data.E0(:,3),'h','LineWidth',2,...
                     'MarkerEdgeColor','k',...
                     'MarkerFaceColor','r',...
                     'MarkerSize',10);
mesh(data.map_x,data.map_y,data.map_z); %生成由X，Y和Z指定的网线面 
%plot3(data.node(:,2),data.node(:,1),data.node(:,3),'*')
colormap ; %颜色映射即色图，将当前图窗的颜色图设置为预定义的颜色图之一 
colorbar; %添加色标 %
axis([0,100,0,100,0,80]); %坐标限定 
shading interp; %对曲面或图形对象的颜色着色进行色彩的插值处理，使色彩平滑过渡 
legend('起点','终点')
title('三维地形地图')
grid 
%%
dim=length(data.E0(:,1))+2*prod(data.mapSize0);
lb=0.6;
ub=1.2;
fobj=@aimFcn_1; 
option.lb=lb; %下限
option.ub=ub; %上限
option.dim=dim;%决策变量个数
if length(option.lb)==1 %判断是否成立，如果成立就往下进行
    option.lb=ones(1,option.dim)*option.lb; %统一长度，每个决策变量对应一个lb和ub
    option.ub=ones(1,option.dim)*option.ub; %统一长度，每个决策变量对应一个lb和ub
end
option.fobj=fobj;
option.showIter=0;

%% 算法参数设置
% 基本参数
option.numAgent=10;        %初始解个体数
option.maxIteration=5;    %最大迭代次数
% 遗传算法
option.p1_GA=0.85;
option.p2_GA=0.1;
% 粒子群
option.w_pso=0.05;
option.c1_pso=2;
option.c2_pso=2;

str_legend=[{'PGA'},{'GGO'},{'ALA'},{'FGO'},{'RFO'}];
%% 初始化种群个体
x=ones(option.numAgent,option.dim);
y=ones(option.numAgent,1);
for i=1:option.numAgent
    x(i,:)=rand(size(option.lb)).*(option.ub-option.lb)+option.lb;
    [y(i),~]=option.fobj(x(i,:),option,data);
end
% YY(ii)=min(y);
% end
%% 使用算法求解
rng(1)
tic
[bestY(1,:),bestX(1,:),recording(1)]=PGA_SanWei(x,y,option,data);
tt(1)=toc;
rng(1)
tic
[bestX(2,:),bestY(2,:),recording(2)]=GGO_SanWei(x,y,option,data);
tt(2)=toc;
rng(1)
tic
[bestY(3,:),bestX(3,:),recording(3)]=ALA_SanWei(x,y,option,data);
tt(3)=toc;
rng(1)
tic
[bestY(4,:),bestX(4,:),recording(4)]=FGO_SanWei(x,y,option,data);
tt(4)=toc;
rng(1)
tic
[bestY(5,:),bestX(5,:),recording(5)]=RFO_SanWei(x,y,option,data);
tt(5)=toc;

figure
hold on
for i=1:length(recording)
    plot(log(recording(i).bestFit),'LineWidth',2)
end
xlabel('迭代次数')
ylabel('适应度值')
legend(str_legend)
title('适应度函数曲线(log)')
%% 输出结果
str='PGA算法'
[~,result1]=aimFcn_1(bestX(1,:),option,data);
newPath1=drawPC(result1,option,data,str);
str='GGO算法'
[~,result2]=aimFcn_1(bestX(2,:),option,data);
newPath2=drawPC(result2,option,data,str);
str='ALA算法'
[~,result3]=aimFcn_1(bestX(3,:),option,data);
newPath3=drawPC(result3,option,data,str);
str='FGO算法'
[~,result4]=aimFcn_1(bestX(4,:),option,data);
newPath4=drawPC(result4,option,data,str);
str='RFO算法'
[~,result5]=aimFcn_1(bestX(5,:),option,data);
newPath5=drawPC(result5,option,data,str);
[newPath11]=drawPC_duibi(result1,result2,result3,result4,result5,option,data,str);
