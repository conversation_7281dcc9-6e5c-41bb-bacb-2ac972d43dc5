% 人工蜂群算法
classdef ABC_Base  < Algorithm_Impl
    
    properties
        %算法名称
        name = 'ABC';
        % 蜜源最大开采次数
        time_max = 60;
        % 雇佣蜂占群体中的概率
        employed_rate = 0.2;
        % 雇佣蜂类型
        EMPLOYED = 1;
        % 非雇佣蜂类型
        UNEMPLOYED = 2;
        % 侦查蜂类型
        SCOUT = 3;
    end
    
    % 外部可调用的方法
    methods
        function self = ABC_Base(dim,size,iter_max,range_min_list,range_max_list)
            % 调用父类构造函数
            self@Algorithm_Impl(dim,size,iter_max,range_min_list,range_max_list);
            self.name ='ABC';
        end
    end
    
    % 继承重写父类的方法
    methods (Access = protected)
        % 初始化种群
        function init(self)
            init@Algorithm_Impl(self)
            % 初始化种群
            for i = 1:self.size
                unit = ABC_Unit();
                % 随机初始化位置：rand(0,1).*(max-min)+min
                unit.position = unifrnd(self.range_min_list,self.range_max_list);
                % 计算适应度值
                unit.value = self.cal_fitfunction(unit.position);
                % 设置初始开采次数为0
                unit.times = 0;
                % 设置默认身份为侦查蜂
                unit.type = self.SCOUT;
                % 将个体加入群体数组
                self.unit_list = [self.unit_list,unit];
            end
        end
        
        % 每一代的更新
        function update(self,iter)
            % 设置雇佣蜂
            self.set_employed();
            % 更新个体
            for i = 1:self.size
                % 根据不同类型选择不同的更新方式
                switch self.unit_list(i).type
                    case self.EMPLOYED
                        % 更新雇佣蜂
                        self.update_employed(i);
                    case self.UNEMPLOYED
                        % 更新非雇佣蜂
                        % 轮盘赌选择目标
                        goal_id = self.roulette();
                        self.update_unemployed(i,goal_id);
                    case self.SCOUT
                        % 更新侦查蜂
                        self.update_scout(i);
                    otherwise
                        continue
                end
            end
            % 检查开采次数
            self.check_times();
            update@Algorithm_Impl(self,iter)
        end
        
        % 设置雇佣蜂（当雇佣蜂数量为0时）
        function set_employed(self)
            % 计算种群中雇佣蜂数量
            employed_num = 0;
            for i = 1:self.size
                if(self.unit_list(i).type == self.EMPLOYED)
                    employed_num = employed_num + 1;
                end
            end
            
            if employed_num > 0
                % 如果雇佣蜂数量不为0则直接跳出
                return
            end
            
            % 求最大值则降序排列
            [value,index] = sort([self.unit_list.value],'descend');
            for i=1:self.size
                if(i<self.employed_rate*self.size+1)
                    % 将排名靠前的部分蜜蜂设为雇佣蜂
                    self.unit_list(index(i)).type = self.EMPLOYED;
                else
                    % 将其他蜜蜂设为非雇佣蜂
                    self.unit_list(index(i)).type = self.UNEMPLOYED;
                end
            end
        end
        
        % 更新雇佣蜂位置
        function update_employed(self,id)
            rand_id_list = randperm(self.size);
            rand_id = rand_id_list(1);
            if(rand_id == id)
                rand_id = rand_id_list(2);
            end
            rnd = unifrnd(-1,1,1,self.dim);
            % 计算新蜜源位置
            new_pos = self.unit_list(id).position+rnd.*(self.unit_list(id).position-self.unit_list(rand_id).position);
            % 越界检查
            new_pos = self.get_out_bound_value(new_pos);
            new_value = self.cal_fitfunction(new_pos);
            if (new_value > self.unit_list(id).value)
                % 如果新位置更优则更新
                self.unit_list(id).position = new_pos;
                self.unit_list(id).value = new_value;
                self.unit_list(id).times = 0;
            else
                % 否则开采次数+1
                self.unit_list(id).times = self.unit_list(id).times +1 ;
            end
        end
        
        % 更新非雇佣蜂位置
        function update_unemployed(self,id,goal_id)
            % 随机选择一个雇佣蜂作为目标
            rand_id_list = randperm(self.size);
            rand_id = rand_id_list(1);
            if(rand_id == goal_id)
                rand_id = rand_id_list(2);
            end
            
            new_pos = self.unit_list(goal_id).position+unifrnd(-1,1,1,self.dim).*(self.unit_list(goal_id).position-self.unit_list(rand_id).position);
            new_pos = self.get_out_bound_value(new_pos);
            new_value = self.cal_fitfunction(new_pos);
            
            if(new_value> self.unit_list(goal_id).value)
                % 如果新蜜源更优则更新蜜源
                self.unit_list(id).value = new_value;
                self.unit_list(id).position = new_pos;
                self.unit_list(id).type = self.EMPLOYED;
                self.unit_list(id).times = 0;
                % 原雇佣蜂变为非雇佣蜂
                self.unit_list(goal_id).type = self.UNEMPLOYED;
                self.unit_list(goal_id).times = 0;
            elseif(new_value >  self.unit_list(id).value)
                % 如果新蜜源差于老蜜源，但由于非雇佣蜂位置
                self.unit_list(id).value = new_value;
                self.unit_list(id).position = new_pos;
                self.unit_list(goal_id).times = self.unit_list(goal_id).times + 1;
            else
                % 否则，蜜源开采次数+1
                self.unit_list(goal_id).times = self.unit_list(goal_id).times + 1;
            end
        end
        
        % 更新侦查蜂
        function update_scout(self,id)
            
            new_pos = unifrnd(self.range_min_list,self.range_max_list);
            new_pos = self.get_out_bound_value(new_pos);
            self.unit_list(id).position = new_pos;
            self.unit_list(id).value = self.cal_fitfunction(new_pos);
            self.unit_list(id).times = 0;
            
            % 计算种群中雇佣蜂数量
            employed_num = 0;
            for i = 1:self.size
                if(self.unit_list(i).type == self.EMPLOYED)
                    employed_num = employed_num + 1;
                end
            end
            
            if (employed_num < self.size*self.employed_rate)
                self.unit_list(id).type = self.EMPLOYED;
            else
                self.unit_list(id).type = self.UNEMPLOYED;
            end
        end
        
        % 检查蜜源开采次数，是否需要放弃，成为侦查蜂
        function check_times(self)
            for i = 1:self.size
                if (self.unit_list(i).type == self.EMPLOYED)
                    if(self.unit_list(i).times > self.time_max)
                        % 将开采次数设为0
                        self.unit_list(i).times = 0;
                        % 转化为侦查蜂
                        self.unit_list(i).type = self.SCOUT;
                    end
                end
            end
        end
        
        % 轮盘赌选择蜜源
        function goal_id = roulette(self)
            % 获取轮盘赌值
            roulette_rate = self.get_roulette_rate();
            % 群体轮盘赌值之和
            roulette_sum = sum(roulette_rate);
            goal_id = 1;

            for i = 1:self.size
                if(self.unit_list(i).type ~= self.EMPLOYED)
                    % 如果不是雇佣蜂则跳过
                    continue
                end
                % 当前轮盘赌值
                roulette_temp = roulette_rate(1);
                rand = unifrnd(0, roulette_sum);
                % 随机数落在了哪个id的轮盘区间
                for j = 1:self.size
                    if (j == self.size)
                        goal_id = self.size;
                        return
                    elseif(rand < roulette_temp)
                        goal_id = j;
                        return
                    end
                    roulette_temp = roulette_temp + roulette_rate(j);
                end
            end
        end
        
        % 计算轮盘赌概率
        function rate_list = get_roulette_rate(self)
            rate_list = zeros(1,self.size);
            roulette_value_min = realmax('double');
            
            % 计算出最小的适应度值
            for i = 1:self.size
                if(self.unit_list(i).type ~= self.EMPLOYED)
                    % 如果不是雇佣蜂则跳过
                    continue
                end
                rate_list(i) = self.unit_list(i).value;
                if (rate_list(i) < roulette_value_min)
                    roulette_value_min = rate_list(i);
                end
            end
            % 计算出每个个体的轮盘赌数值
            for i = 1:self.size
                if(self.unit_list(i).type ~= self.EMPLOYED)
                    % 如果不是雇佣蜂则跳过
                    continue
                end
                rate_list(i) = rate_list(i) - roulette_value_min;
            end
        end
        
    end
end
