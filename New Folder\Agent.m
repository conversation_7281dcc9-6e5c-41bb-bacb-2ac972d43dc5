% 智能体基类
% 公共状态：位置，速度，艏向，历史航迹，可选择动作位置，观测范围
% 保护状态：最大速度，最大转艏角度，最大观测半径，单步路径
classdef Agent < handle
    properties       
        position; % 智能体位置       
        speed; % 智能体速度        
        heading; % 智能体航向  
        
        path; % 智能体历史航迹
        position_history; % 历史位置

        optional_position; % 智能体可选择位置       
        ob_area % 智能体观测范围
    end

    properties (Access = protected)
        speed_max; % 智能体最大速度
        dt_heading_max; % 智能体最大转艏角度
        Ob_radius_max; % 智能体最大观测半径     

        predicted_steps; % 步数

        path_1step; % 单步路径        
       
    end

    methods
        % 构造函数
        function self = Agent(initial_position, initial_speed, initial_heading, speed_max, dt_heading_max, Ob_radius_max, predicted_steps, areaSize, gridSize, decmake_time)
            self.position = initial_position;
            self.speed = initial_speed;
            self.heading = initial_heading;
            self.path = [];
            self.position_history = [];
            
            self.path_1step = [];

            self.speed_max = speed_max;
            self.dt_heading_max = dt_heading_max;
            self.Ob_radius_max = Ob_radius_max;

            self.predicted_steps = predicted_steps;

%             self.optional_position = self.get_optional_positions_1step(self.position, self.heading, self.speed_max, self.dt_heading_max, areaSize, gridSize, decmake_time);
            self.optional_position = self.get_feas_positions_m_step(self.position, self.heading, self.speed_max, self.dt_heading_max, self.predicted_steps, areaSize, gridSize, decmake_time);
                                            
            self.ob_area = self.get_ob_area(self.position, self.Ob_radius_max, areaSize, gridSize);
        end

        function self = agent_update(self, next_position, areaSize, gridSize, decmake_time)
            self.path_1step = self.getLineGridCells(self.position, next_position);
            self.path = [self.path; self.path_1step(2:end, :)];
            self.position_history = [self.position_history; self.position];
            self.position = next_position;
            dt_position = self.position - self.position_history(end, :);
            r = sqrt(dt_position(1)^2 + dt_position(2)^2);
            self.speed = r / decmake_time;
            self.heading = atan2(dt_position(2), dt_position(1));

%             self.optional_position = self.get_optional_positions_1step(self.position, self.heading, self.speed_max, self.dt_heading_max, areaSize, gridSize, decmake_time);
            self.optional_position = self.get_feas_positions_m_step(self.position, self.heading, self.speed_max, self.dt_heading_max, self.predicted_steps, areaSize, gridSize, decmake_time);
           
            self.ob_area = [];
            for i_p = 1:size(self.path_1step, 1)
                self.ob_area = [self.ob_area; self.get_ob_area(self.path_1step(i_p, :), self.Ob_radius_max, areaSize, gridSize)];
                self.ob_area = unique(self.ob_area, 'rows');
            end

        end
    end

    methods (Access = protected)
        function Fes_pos = get_feas_positions_m_step(self, position, heading, speed_max, dt_heading_max, predicted_steps, areaSize, gridSize, decmake_time)
            Fes_pos = {};
            for k = 1:predicted_steps
                if k==1
                    Fes_pos{k} = self.get_optional_positions_1step(position, heading, speed_max, dt_heading_max, areaSize, gridSize, decmake_time);
                else
                    for j=1:size(Fes_pos{k-1}, 1)
                        dt_pos_pre = Fes_pos{k-1}(j,:) - position; % 多步移动位置变化
                        heading_pre = atan2(dt_pos_pre(2), dt_pos_pre(1)); % 多步移动艏向变化

                        Fes_pos{k}{j} = self.get_optional_positions_1step(Fes_pos{k-1}(j,:), heading_pre, speed_max, dt_heading_max, areaSize, gridSize, decmake_time);
                    end
                end
            end
        end

        function coveredGrids = get_optional_positions_1step(self, position, heading, speed_max, dt_heading_max, areaSize, gridSize, decmake_time)
            % --- 提取可到达扇形参数 ---
            centerGridIndex = position;
            radius = speed_max * decmake_time; % 扇形半径
            if heading < 0
                heading = heading + 2*pi;
            end
            angleStart = heading - dt_heading_max;
            angleEnd = heading + dt_heading_max;

            % --- 提取栅格参数 ---
            cellSize = gridSize;
            gridXMin = 0;
            gridYMin = 0;
            gridXMax = areaSize(1);
            gridYMax = areaSize(2);

            % --- 计算圆心的连续坐标 (栅格中心) ---
            % 假设(1,1)栅格的中心在(0.5*cellSize, 0.5*cellSize)
            cx = (centerGridIndex(1) - 0.5) * cellSize;
            cy = (centerGridIndex(2) - 0.5) * cellSize;

            % --- 确定需要检查的栅格索引范围 ---
            % 考虑到扇形半径，可以缩小检查范围以提高效率
            minCheckX = floor((cx - radius) / cellSize);
            maxCheckX = ceil((cx + radius) / cellSize);
            minCheckY = floor((cy - radius) / cellSize);
            maxCheckY = ceil((cy + radius) / cellSize);

            % 将检查范围限制在地图的实际栅格索引范围内
            xIndicesFull = floor(gridXMin/cellSize)+1:ceil(gridXMax/cellSize); % 确保索引从1开始
            yIndicesFull = floor(gridYMin/cellSize)+1:ceil(gridYMax/cellSize); % 确保索引从1开始

            % 将检查范围与地图范围求交集
            xIndices = max(minCheckX, min(xIndicesFull)):min(maxCheckX, max(xIndicesFull));
            yIndices = max(minCheckY, min(yIndicesFull)):min(maxCheckY, max(yIndicesFull));

            % 预分配存储，估算最大可能数量，可以避免多次扩容
            % 考虑到最坏情况下的最大栅格数，或者预估一个合适的值
            estimatedMaxGrids = (2*ceil(radius/cellSize) + 2)^2;
            coveredGrids = zeros(estimatedMaxGrids, 2); % 使用zeros预分配
            count = 0; % 实际计数器

            % --- 遍历并判断每个栅格 ---
            for ix = xIndices
                for iy = yIndices
                    % 栅格中心坐标
                    gx = (ix - 0.5) * cellSize;
                    gy = (iy - 0.5) * cellSize;

                    % 计算距离和角度
                    dx = gx - cx;
                    dy = gy - cy;
                    r = sqrt(dx^2 + dy^2);

                    % 快速跳过距离过大的栅格
                    if r > radius || r == 0
                        continue;
                    end

                    theta = atan2(dy, dx); % 获取角度（度）
                    % 角度标准化到 [0, 360) 范围
                    if theta < 0
                        theta = theta + 2*pi;
                    end

                    % 处理扇形角度跨越360度的情况
                    startAngle = mod(angleStart, 2*pi);
                    endAngle = mod(angleEnd, 2*pi);

                    inAngleRange = false;
                    if startAngle <= endAngle
                        % 正常情况
                        inAngleRange = (theta >= startAngle) && (theta <= endAngle);
                    else
                        % 跨越360度的情况，如从300度到30度
                        inAngleRange = (theta >= startAngle) || (theta <= endAngle);
                    end

                    % 最终判断
                    if inAngleRange
                        count = count + 1;
                        coveredGrids(count, :) = [ix, iy];
                    end
                end
            end

            % 裁剪到实际使用的部分
            coveredGrids = coveredGrids(1:count, :);

            if isempty (coveredGrids)
                %                 disp('@@@@@@无可行点@@@@@@@@');
                % 在边上
                isLeftCol = (centerGridIndex(1) == min(xIndicesFull));
                isRightCol = (centerGridIndex(1) == max(xIndicesFull));
                isBottomRow = (centerGridIndex(2) == min(yIndicesFull));
                isTopRow = (centerGridIndex(2) == max(yIndicesFull));

                % 在角落
                isCorner = (isTopRow || isBottomRow) && (isLeftCol || isRightCol);

                if isCorner
                    % 方向
                    if isTopRow && isLeftCol % 左上角
                        for iy = yIndices
                            ix = centerGridIndex(1);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius || r == 0
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                        for ix = xIndices
                            iy = centerGridIndex(2);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius || r == 0
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                    elseif isTopRow && isRightCol % 右上角
                        for iy = yIndices
                            ix = centerGridIndex(1);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius || r == 0
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                        for ix = xIndices
                            iy = centerGridIndex(2);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius || r == 0
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                    elseif isBottomRow && isLeftCol % 左下角
                        for iy = yIndices
                            ix = centerGridIndex(1);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius || r == 0
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                        for ix = xIndices
                            iy = centerGridIndex(2);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius || r == 0
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                    elseif isBottomRow && isRightCol % 右下角
                        for iy = yIndices
                            ix = centerGridIndex(1);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius || r == 0
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                        for ix = xIndices
                            iy = centerGridIndex(2);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius || r == 0
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                    end
                else
                    if isLeftCol || isRightCol
                        for iy = yIndices
                            ix = centerGridIndex(1);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius || r == 0
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                    elseif isBottomRow || isTopRow
                        for ix = xIndices
                            iy = centerGridIndex(2);
                            gx = (ix - 0.5) * cellSize;
                            gy = (iy - 0.5) * cellSize;

                            % 计算距离和角度
                            dx = gx - cx;
                            dy = gy - cy;
                            r = sqrt(dx^2 + dy^2);

                            if r > radius
                                continue;
                            end

                            count = count + 1;
                            coveredGrids(count, :) = [ix, iy];
                        end
                    end

                end
                % 裁剪到实际使用的部分
                coveredGrids = coveredGrids(1:count, :);
            end
        end

        function Obarea = get_ob_area(self, position, Ob_radius, areaSize, gridSize)
            % --- 提取某位置观测范围 ---
            centerGridIndex = position;
            radius =  Ob_radius; % 观测区域半径
            % --- 提取栅格参数 ---
            cellSize = gridSize;
            gridXMin = 0;
            gridYMin = 0;
            gridXMax = areaSize(1);
            gridYMax = areaSize(2);
            % --- 计算圆心的连续坐标 (栅格中心) ---
            cx = (centerGridIndex(1) - 0.5) * cellSize;
            cy = (centerGridIndex(2) - 0.5) * cellSize;
            % --- 确定需要检查的栅格索引范围 ---
            minCheckX = floor((cx - radius) / cellSize);
            maxCheckX = ceil((cx + radius) / cellSize);
            minCheckY = floor((cy - radius) / cellSize);
            maxCheckY = ceil((cy + radius) / cellSize);
            % 将检查范围限制在地图的实际栅格索引范围内
            xIndicesFull = floor(gridXMin/cellSize)+1:ceil(gridXMax/cellSize);
            yIndicesFull = floor(gridYMin/cellSize)+1:ceil(gridYMax/cellSize);
            % 将检查范围与地图范围求交集
            xIndices = max(minCheckX, min(xIndicesFull)):min(maxCheckX, max(xIndicesFull));
            yIndices = max(minCheckY, min(yIndicesFull)):min(maxCheckY, max(yIndicesFull));

            estimatedMaxGrids = (2*ceil(radius/cellSize) + 2)^2;
            Obarea = zeros(estimatedMaxGrids, 2); % 使用zeros预分配
            count = 0; % 实际计数器
            % --- 遍历并判断每个栅格 ---
            for ix = xIndices
                for iy = yIndices
                    % 栅格中心坐标
                    gx = (ix - 0.5) * cellSize;
                    gy = (iy - 0.5) * cellSize;
                    % 计算距离和角度
                    dx = gx - cx;
                    dy = gy - cy;
                    r = sqrt(dx^2 + dy^2);
                    % 快速跳过距离过大的栅格
                    if r > radius
                        continue;
                    end
                    count = count + 1;
                    Obarea(count, :) = [ix, iy];
                end
            end
            % 裁剪到实际使用的部分
            Obarea = Obarea(1:count, :);
        end

        function gridCells = getLineGridCells(self, position, next_position)
            x1 = round(position(1));    y1 = round(position(2));
            x2 = round(next_position(1));    y2 = round(next_position(2));

            dx = abs(x2 - x1); dy = abs(y2 - y1);
            sx = sign(x2 - x1); sy = sign(y2 - y1);
            if dx > dy
                err = dx / 2;
            else
                err = -dy /2;
            end
            x = x1; y = y1;
            gridCells = [];

            while true
                gridCells = [gridCells; x, y];
                if x == x2 && y == y2
                    break;
                end
                e2 = err;
                if e2 > -dx
                    err = err - dy;
                    x = x + sx;
                end
                if e2 < dy
                    err = err + dx;
                    y = y + sy;
                end
            end
        end
    end

end