% 粒子群算法
classdef PSO_Base < Algorithm_Impl
    %% 算法属性及参数
    properties
        % 算法名称：
        name = 'PSO';
        % 最大速度限制
        velocity_max_list;
        % 自我学习系数
        C1 = 2;
        % 全局学习系数
        C2 = 2;
        % 惯性系数
        W = 1;
    end
    
    %% 外部可调用的方法
    methods
        % 构造函数
        function self = PSO_Base(dim,size,iter_max,range_min_list,range_max_list)
            self@Algorithm_Impl(dim,size,iter_max,range_min_list,range_max_list);
            % 初始化速度上限(为取值范围长度的1/10）
            self.velocity_max_list = (self.range_max_list - self.range_min_list)*0.1;
        end
    end 
    
    %% 继承重写父类的方法
    methods (Access = protected)
        % 初始化种群
        function init(self)
            init@Algorithm_Impl(self)
            % 初始化种群中的每个个体
            for i = 1:self.size 
                unit = PSO_Unit();
                % 随机初始化位置：rand(0,1).*(max-min)+min
                unit.position = unifrnd(self.range_min_list,self.range_max_list);
                % 初始化时，历史最优就是当前位置
                unit.position_best = unit.position;
                % 随机初始化速度：rand(0,1).*(max-(-max))+(-max)
                unit.velocity = unifrnd(-self.velocity_max_list , self.velocity_max_list);
                unit.value = self.cal_fitfunction(unit.position);
                % 将个体加入群体数组
                self.unit_list = [self.unit_list,unit];
            end
        end
        
        % 每一代的更新
        function update(self,iter)
            update@Algorithm_Impl(self,iter)
            for i = 1:self.size
                % 更新该个体的速度
                self.update_velocity(i)
                % 更新该个体的位置
                self.update_position(i)
            end
        end
        
        % 更新个体的速度
        function update_velocity(self,id)
            % 获取当前个体实例
            unit = self.unit_list(id);
            % 计算新的速度
            % 公式： v_new = W*v_old+C1*rand(0,1).*(p_best-unit_pos)+C2*rand(0,1).*(g_best-unit_pos)
            velocity_new = self.W*unit.velocity+self.C1*rand(1,self.dim).*(unit.position_best-unit.position)+self.C2*rand(1,self.dim).*(self.position_best-unit.position);
            velocity_new = self.get_out_bound_value(velocity_new,-self.velocity_max_list,self.velocity_max_list);
            % 修改该个体速度
            unit.velocity = velocity_new;
            % 保存修改值
            self.unit_list(id) = unit;
        end
        
        % 更新个体的位置
        function update_position(self,id)
            unit = self.unit_list(id);
            % 计算出新的位置
            position_new = unit.position + unit.velocity;
            % 个体移动到该位置
            unit.position = position_new;
            % 计算该位置的适应度值
            value = self.cal_fitfunction(unit.position);
            if (value > unit.value)
                % 只记录历史最优值
                unit.value = value;
                % 由于历史最优值时更新历史最优位置
                unit.position_best = unit.position;
            end
            % 保存修改值
            self.unit_list(id) = unit;
        end
       %%
    end
end
