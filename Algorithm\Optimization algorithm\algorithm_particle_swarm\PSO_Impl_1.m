% PSO修改，惯性系数W线性递减，由1->0
classdef PSO_Impl_1 < PSO_Base
  
   % 外部可调用的方法
   methods
       function self = PSO_Impl_1(dim,size,iter_max,range_min_list,range_max_list)
           % 调用父类构造函数设置参数
            self@PSO_Base(dim,size,iter_max,range_min_list,range_max_list);
            self.name = 'PSO_1';
       end
   end 
   
   % 重写父类的方法
   methods (Access = protected)
       % 每一代的更新
       function update(self,iter)
           update@PSO_Base(self,iter)
           % 惯性系数线性递减，由1->0
           self.W = (1.0-iter/self.iter_max);
       end 
   end
   
end