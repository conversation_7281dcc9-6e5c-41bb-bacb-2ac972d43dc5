% 差分进化算法
classdef DE_Base < Algorithm_Impl
    
    properties
        % 算法名称
        name = 'DE';
        % 交叉率
        cross_rate;
        % 变异率
        alter_factor;
        
    end
    
    % 外部可调用的方法
    methods
        function self = DE_Base(dim,size,iter_max,range_min_list,range_max_list)
            % 调用父类构造函数
            self@Algorithm_Impl(dim,size,iter_max,range_min_list,range_max_list);
            self.cross_rate = 0.3;
            self.alter_factor = 0.5;
        end
    end
    
    % 继承重写父类的方法
    methods (Access = protected)
        % 初始化种群
        function init(self)
            init@Algorithm_Impl(self)
            %初始化种群
            for i = 1:self.size 
                unit = DE_Unit();
                % 随机初始化位置：rand(0,1).*(max-min)+min
                unit.position = unifrnd(self.range_min_list,self.range_max_list);
                unit.position_new = unit.position;
                % 计算适应度值
                unit.value = self.cal_fitfunction(unit.position);
                % 将个体加入群体数组
                self.unit_list = [self.unit_list,unit];
            end
        end
        
        % 每一代的更新
        function update(self,iter)
            update@Algorithm_Impl(self,iter)
            % 变异
            self.altered();
            % 交叉
            self.cross();
            % 选择
            self.choose();
        end
        
        % 变异
        function altered(self)
            % 遍历所有个体
            for i = 1:self.size
                % 在群体中随机选择3个个体
                % 1.先将群体的id乱序
                % 2.从乱序中选择前3个id
                rand_id_list = randperm(self.size);
                r1 = rand_id_list(1);
                r2 = rand_id_list(2);
                r3 = rand_id_list(3);
                if (r1 == i)
                    r1 =rand_id_list(4);
                end
                if (r2 == i)
                    r2 =rand_id_list(4);
                end
                if (r3 == i)
                    r3 =rand_id_list(4);
                end
                
                % 变异得到新位置
                % new_pos = rand_1_pos+f*(rand_2_pos - rand_3_pos)
                new_pos = self.unit_list(r1).position + self.alter_factor*(self.unit_list(r2).position - self.unit_list(r3).position);
                new_pos = self.get_out_bound_value(new_pos);
                % 保存该变异位置
                self.unit_list(i).position_new = new_pos;
            end
        end
        
        % 交叉
        function cross(self)
            % 遍历步长为2，相邻两个个体交叉
            for i = 1:self.size
                % 随机选择一维
                cur_dim = unidrnd(self.dim);
                for d = 1:self.dim
                    % 满足交叉条件则将原对应个体对应维度保留
                    r = unifrnd (0,1);
                    if(r<self.cross_rate || d == cur_dim)
                        % 保持不变
                    else
                        self.unit_list(i).position_new(d) = self.unit_list(i).position(d);
                    end
                end
            end
        end
        
        % 选择个体,贪心算法保留对应个体
        function choose(self)
            for i = 1:self.size
                new_value = self.cal_fitfunction(self.unit_list(i).position_new);
                if (new_value > self.unit_list(i).value)
                    self.unit_list(i).value = new_value;
                    self.unit_list(i).position = self.unit_list(i).position_new;
                end
            end
        end
        
        
    end
end