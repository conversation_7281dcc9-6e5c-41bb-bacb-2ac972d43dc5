% 离散差分算法
classdef DE_Base_Discrete < DE_Base
    
    properties
        discrete_points; % 离散点集
    end
    
    methods
        function self = DE_Base_Discrete(dim, size, iter_max, discrete_points)
            % 从离散点计算范围
            range_min = min(discrete_points, [], 1);
            range_max = max(discrete_points, [], 1);
            
            self@DE_Base(dim, size, iter_max, range_min, range_max);
            self.discrete_points = discrete_points;
            self.name = 'DE_Base_Discrete';
        end
    end
    
    methods (Access = protected)
        % 初始化
        function init(self)
            init@Algorithm_Impl(self)
            for i = 1:self.size 
                unit = DE_Unit();
                % 从离散点中随机选择
                idx = randi(size(self.discrete_points, 1));
                unit.position = self.discrete_points(idx, :);
                unit.position_new = unit.position;
                unit.value = self.cal_fitfunction(unit.position);
                self.unit_list = [self.unit_list, unit];
            end
        end
        
        % 变异
        function altered(self)
            for i = 1:self.size
                % 在群体中随机选择3个个体
                % 1.先将群体的id乱序
                % 2.从乱序中选择前3个id
                rand_id_list = randperm(self.size);
                r1 = rand_id_list(1);
                r2 = rand_id_list(2);
                r3 = rand_id_list(3);
                if (r1 == i)
                    r1 = rand_id_list(4); 
                end
                if (r2 == i) 
                    r2 = rand_id_list(4); 
                end
                if (r3 == i)
                    r3 = rand_id_list(4); 
                end
                
                % 差分变异
                new_pos = self.unit_list(r1).position + self.alter_factor * (self.unit_list(r2).position - self.unit_list(r3).position);
                
                % 映射到最近的离散点
                new_pos = self.go_to_discrete(new_pos);
                % 保存该变异位置
                self.unit_list(i).position_new = new_pos;
            end
        end
        
        % 交叉
        function cross(self)
            for i = 1:self.size
                cur_dim = unidrnd(self.dim);
                for d = 1:self.dim
                    r = unifrnd(0, 1);
                    if r >= self.cross_rate && d ~= cur_dim
                        self.unit_list(i).position_new(d) = self.unit_list(i).position(d);
                    end
                end
                % 交叉后仍在离散点集合中
                self.unit_list(i).position_new = self.go_to_discrete(self.unit_list(i).position_new);
            end
        end
        
        % 映射到最近的离散点
        function discrete_pos = go_to_discrete(self, continuous_pos)
            distances = sum((self.discrete_points - continuous_pos).^2, 2);
            [~, min_idx] = min(distances);
            discrete_pos = self.discrete_points(min_idx, :);
        end
    end
end